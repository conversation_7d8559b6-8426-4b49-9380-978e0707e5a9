package com.eyuan.pay.request;

import com.eyuan.pay.enums.AppPlatformType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 订单支付通知
 * @date 2020-09-02 16:32
 */
@Data
@ApiModel(description = "商品支付完成通知请求")
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayNoticeRequest extends BasePayRequest {

	private static final long serialVersionUID = 8568073209456379238L;

	@ApiModelProperty(value="支付结果状态 [true:成功; false:失败;]")
	private Boolean payResult;

	//  [格式: yyyy-MM-dd HH:mm:ss]
	@ApiModelProperty(value="支付时间")
	private Date payTime;

	@ApiModelProperty(value="支付订单号 即: 业务平台订单号")
	private String payOrderId;
	/**
	 * 渠道订单号
	 */
	@ApiModelProperty(value="渠道订单号 即：三方支付订单号")
	private String channelOrderNo;

	@ApiModelProperty(value="支付金额 单位:元")
	private BigDecimal payAmt;

	@ApiModelProperty(value="支付工具类型 [0:支付宝; 1:微信]")
	private Integer payTool;

	@ApiModelProperty(value="App平台类型")
	private AppPlatformType appPlatformType;
}
