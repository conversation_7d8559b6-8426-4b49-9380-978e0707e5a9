package com.eyuan.pay.request;

import com.eyuan.pay.enums.AppPlatformType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @description 退款通知
 * @date 2020-09-02 16:32
 */
@Data
@ApiModel(description = "退款完成通知请求")
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderRefundNoticeRequest extends BasePayRequest {

	private static final long serialVersionUID = 8568073209456379238L;

	@ApiModelProperty(value="结果状态 [true:成功; false:失败;]")
	private Boolean returnCode;

	@ApiModelProperty(value="当return_code为false时返回信息为错误原因")
	private String returnMsg;


	@ApiModelProperty(value="支付订单号 即: 业务平台订单号")
	private String payOrderId;

	@ApiModelProperty(value="退款单号")
	private String refundId;

	@ApiModelProperty(value="三方退款单号")
	private String thirdRefundId;

	@ApiModelProperty(value="App平台类型")
	private AppPlatformType appPlatformType;
}
