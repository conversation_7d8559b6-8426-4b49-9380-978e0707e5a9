package com.eyuan.pay.request;

import com.eyuan.pay.enums.AppPlatformType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算审核通知请求
 * <AUTHOR>
 * @date 2025/5/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("结算通知请求")
public class SettlementNoticeRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="App平台类型")
    private AppPlatformType appPlatformType;

    @ApiModelProperty("交易号")
    private String orderNo;

    @ApiModelProperty("三方交易号")
    private String thirdOrderNo;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("结算类型")
    private Integer settlementType;

    @ApiModelProperty("用户类型")
    private String userType;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("交易类型")
    private Integer tradeType;

    @ApiModelProperty("交易状态(success-成功,failed-失败)")
    private String tradeStatus;

    @ApiModelProperty("失败原因")
    private String failReason;

    @ApiModelProperty("审核状态(APPROVED-通过,REJECTED-拒绝)")
    private String auditStatus;

    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("租户ID")
    private String tenantId;


}