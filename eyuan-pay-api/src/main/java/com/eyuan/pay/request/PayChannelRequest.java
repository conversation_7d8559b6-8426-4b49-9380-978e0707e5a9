package com.eyuan.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 支付请求
 * @date 2020-08-04 11:28
 */
@Data
@ApiModel(description = "支付渠道查询")
@ToString(callSuper = true)
public class PayChannelRequest implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * {@link com.eyuan.pay.enums.PayToolEnum}
	 */
	@ApiModelProperty(value="渠道id")
	@NotBlank(message = "渠道id不能为空")
	private String channelId;

	/**
	 * 租户ID
	 */
	@ApiModelProperty(value="租户ID")
	@NotBlank(message = "租户ID不能为空")
	private String tenantId;

}
