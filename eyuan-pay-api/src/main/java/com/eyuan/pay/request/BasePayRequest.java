package com.eyuan.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 基础请求
 * @date 2020-08-04 11:44
 */
@Data
@ApiModel(description = "基础请求")
public class BasePayRequest implements java.io.Serializable {

	private static final long serialVersionUID = -1843447011675839823L;

	/**
	 * 用户ID
	 */
	@ApiModelProperty(value="用户ID(微信支付，参数为openId)")
	@NotBlank(message = "用户ID不能为空")
	private String userId;

	@ApiModelProperty(value="平台类型 [b2c:商城; home:民宿;], 如果不填，默认为b2c")
	private String platformType;

	@ApiModelProperty(value="租户id")
	private String tenantId;

}
