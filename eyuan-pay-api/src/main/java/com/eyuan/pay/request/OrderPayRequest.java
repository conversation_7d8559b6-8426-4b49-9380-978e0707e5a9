package com.eyuan.pay.request;

import com.eyuan.pay.request.dto.SplitAccountDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 支付请求
 * @date 2020-08-04 11:28
 */
@Data
@ApiModel(description = "商品支付请求")
@ToString(callSuper = true)
public class OrderPayRequest extends BasePayRequest {

	private static final long serialVersionUID = 2278520615037478395L;

	@ApiModelProperty(value="商品ID")
	@NotBlank(message = "商品ID不能为空")
	private String goodsId;

	/**
	 * 商品名称
	 */
	@ApiModelProperty(value="商品名称")
	@NotBlank(message = "商品名称不能为空")
	private String goodsName;

	/**
	 * 金额,单位分
	 */
	@ApiModelProperty(value="金额(单位分)")
	@NotBlank(message = "金额不能为空")
	private String amount;

	/**
	 * 支付订单号
	 */
	@ApiModelProperty(value="支付订单号")
	@NotBlank(message = "支付订单号不能为空")
	private String payOrderId;

	/**
	 * 租户ID
	 */
	/*@ApiModelProperty(value="租户ID")
	private Integer tenantId;*/

	@ApiModelProperty(value="支付渠道 [0:支付宝; 1:微信;2:合利宝]")
	@NotNull(message = "支付渠道")
	private Integer payChannel;


	/**
	 * 付款码
	 */
	@ApiModelProperty(value="付款码")
	private String paymentCode;

	/**
	 * 分账信息
	 */
	@ApiModelProperty(value="分账信息")
	private List<SplitAccountDTO> splitAccountList;

}
