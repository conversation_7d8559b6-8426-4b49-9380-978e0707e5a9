package com.eyuan.pay.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 微信扫码支付(模式一)回调请求-微信发起
 * @date 2020-10-31 16:02
 */
@Data
public class WxNativePayCallbackRequest implements java.io.Serializable {

	private static final long serialVersionUID = -2606408501484215377L;

//	公众账号ID	appid	String(32)	是	wx8888888888888888	微信分配的公众账号ID
	private String appid;

//	用户标识	openid	String(128)	是	o8GeHuLAsgefS_80exEr1cTqekUs	用户在商户appid下的唯一标识
	private String openid;

//	商户号	mch_id	String(32)	是	1900000109	微信支付分配的商户号
	private String mch_id;

//	是否关注公众账号	is_subscribe	String(1)	是	Y	用户是否关注公众账号，仅在公众账号类型支付有效，取值范围：Y或N;Y-关注;N-未关注
	private String is_subscribe;

//	随机字符串	nonce_str	String(32)	是	5K8264ILTKCH16CQ2502SI8ZNMTM67VS	随机字符串，不长于32位。推荐随机数生成算法
	private String nonce_str;

//	商品ID	product_id	String(32)	是	88888	商户定义的商品id 或者订单号
	private String product_id;

//	签名	sign	String(32)	是	C380BEC2BFD727A4B6845133519F3AD6	返回数据签名，签名生成算法
	private String sign;

}
