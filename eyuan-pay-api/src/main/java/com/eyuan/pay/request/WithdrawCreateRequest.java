package com.eyuan.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 提现申请请求
 * <AUTHOR>
 * @date 2024-05-15
 */
@Data
@ApiModel("提现申请请求")
public class WithdrawCreateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提现单号
     */
    @ApiModelProperty(value="提现单号")
    private String withdrawNo;

    /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;

    
    /**
     * 提现金额
     */
    @ApiModelProperty(value="提现金额", required=true)
    private BigDecimal amount;
    
    /**
     * 提现方式(1:银行卡,2:微信,3:支付宝,5:余额)
     */
    @ApiModelProperty(value="提现方式(1:银行卡,2:微信,3:支付宝,5:余额)", required=true)
    private Integer withdrawType;
    
    /**
     * 提现账户ID
     */
    @ApiModelProperty(value="提现账户ID", required=true)
    private String withdrawAccountId;
    
    /**
     * 付款方ID
     */
    @ApiModelProperty(value="付款方ID")
    private String payerId;
    
    /**
     * 付款方类型(1:平台,2:商家,3:其他)
     */
    @ApiModelProperty(value="付款方类型(1:平台,2:商家,3:其他)")
    private Integer payerType;
    
    /**
     * 租户ID
     */
    @ApiModelProperty(value="租户ID")
    private String tenantId;
}