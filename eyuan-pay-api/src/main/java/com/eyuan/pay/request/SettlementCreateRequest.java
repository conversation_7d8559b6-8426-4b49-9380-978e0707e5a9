package com.eyuan.pay.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算新增请求
 * @Author: sunwh
 * @Date: 2025/5/17 16:32
 */
@Data
public class SettlementCreateRequest {

    /**
     * 供应商ID
     */
    @ApiModelProperty("供应商ID")
    private String supplierId;

    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;


    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 订单类型
     */
    @ApiModelProperty(value="订单类型")
    private String orderType;

    @ApiModelProperty("结算到哪")
    private Integer settleTo;

    @ApiModelProperty("账号")
    private String accountNo;

    @ApiModelProperty("结算时间")
    private LocalDateTime settlementTime;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;
}
