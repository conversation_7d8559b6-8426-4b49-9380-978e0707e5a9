package com.eyuan.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 退款请求
 * @date 2020-08-04 14:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString(callSuper = true)
@ApiModel(description = "订单退款请求")
public class OrderRefundRequest extends BasePayRequest {

	private static final long serialVersionUID = 8243505500228982600L;

	@ApiModelProperty(value="支付订单号")
	@NotBlank(message = "支付订单号不能为空")
	private String payOrderId;

	@ApiModelProperty(value="商户退款单号(商城填退款批次号)")
	@NotBlank(message = "商户退款单号 不能为空")
	private String mchRefundNo;

	@ApiModelProperty(value="商品名称")
	@NotBlank(message = "商品名称不能为空")
	private String goodsName;

	@ApiModelProperty(value="订单支付金额(单位分)")
	@NotNull(message = "订单支付金额不能为空")
	private Long orderPayAmt;

	@ApiModelProperty(value="退款金额(单位分)")
	@NotNull(message = "退款金额不能为空")
	private Long refundAmt;

	@ApiModelProperty(value="支付渠道ID")
	private String payChannelId;



}
