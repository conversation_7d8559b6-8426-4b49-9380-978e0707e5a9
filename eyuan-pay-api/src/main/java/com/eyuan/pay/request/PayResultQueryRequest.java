package com.eyuan.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 支付结果查询
 * @date 2020-08-04 14:07
 */
@Data
@ApiModel(description = "支付结果请求")
public class PayResultQueryRequest extends BasePayRequest {

	private static final long serialVersionUID = -5885494418174450397L;

	/**
	 * 支付订单号
	 */
	@ApiModelProperty(value="支付订单号")
	@NotBlank(message = "支付订单号不能为空")
	private String payOrderId;

}
