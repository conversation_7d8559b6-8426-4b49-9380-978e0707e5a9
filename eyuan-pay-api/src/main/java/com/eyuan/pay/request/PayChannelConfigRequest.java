package com.eyuan.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 支付渠道配置请求
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@Data
@ApiModel(description = "支付渠道配置查询")
@ToString(callSuper = true)
public class PayChannelConfigRequest implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 渠道标识
     */
    @ApiModelProperty(value = "渠道标识")
    @NotBlank(message = "渠道标识不能为空")
    private String channelId;
}
