package com.eyuan.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 微信扫码(模式一)生成二维码请求
 * @date 2020-10-31 16:12
 */
@Data
@ApiModel(description = "微信扫码(模式一)生成二维码请求")
public class WxNativePayGenQrCodeRequest implements java.io.Serializable {

	private static final long serialVersionUID = 3440759442174818331L;

	@ApiModelProperty(value="商品id")
	@NotBlank(message = "商品id 不能为空")
	private String productid;

	@ApiModelProperty(value="租户id")
	@NotNull(message = "租户id 不能为空")
	private Integer tenantId;
}
