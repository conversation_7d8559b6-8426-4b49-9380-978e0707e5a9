package com.eyuan.pay.constant;

/**
 * <AUTHOR>
 * @date 2019-06-14
 * <p>
 * 支付相关的常量
 */
public interface PayConstants {

	/**
	 * 支付宝商户交易编号
	 */
	String OUT_TRADE_NO = "out_trade_no";

	/**
	 * 支付宝浏览器标志
	 */
	String ALIPAY = "Alipay";

	/**
	 * 微信浏览器标志
	 */
	String MICRO_MESSENGER = "MicroMessenger";

	/**
	 * 返回码
	 */
	String RESULT_CODE = "result_code";

	/**
	 * 支付状态（支付宝）
	 */
	String TRADE_STATUS = "trade_status";

	/**
	 * 微信支付配置信息key拼接符
	 */
	String WX_API_CONFIG_JOIN = "|";

	/**
	 * 证书路径
	 */
	String CERT_PATH = "/root/cert/";

	/**
	 * 证书名称
	 */
	String CERT_FILE_NAME = "apiclient_cert.p12";


}
