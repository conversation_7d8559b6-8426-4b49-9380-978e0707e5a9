package com.eyuan.pay.constant;

/**
 * <AUTHOR>
 * @date 2019-06-14
 * <p>
 * 支付MQ常量
 */
public interface PayMQConstants {

	/**
	 * 支付消息交换机
	 */
	String MQ_EXCHANGE = "eyuan-pay-exchange";

	/**
	 * 支付通知消息队列(门票)
	 */
	String MQ_PAY_NOTICE_TICKET_QUEUE = "eyuan-pay-notice-ticket-queue";

	/**
	 * 支付通知消息队列(酒店)
	 */
	String MQ_PAY_NOTICE_HOTEL_QUEUE = "eyuan-pay-notice-hotel-queue";

	/**
	 * 支付通知消息队列(b2c)
	 */
	String MQ_PAY_NOTICE_B2C_QUEUE = "eyuan-pay-notice-b2c-queue";

	/**
	 * 退款通知消息队列(门票)
	 */
	String MQ_REFUND_NOTICE_TICKET_QUEUE = "eyuan-pay-refund-notice-ticket-queue";

	/**
	 * 退款通知消息队列(酒店)
	 */
	String MQ_REFUND_NOTICE_HOTEL_QUEUE = "eyuan-pay-refund-notice-hotel-queue";

	/**
	 * 退款通知消息队列(b2c)
	 */
	String MQ_REFUND_NOTICE_B2C_QUEUE = "eyuan-pay-refund-notice-b2c-queue";

	/**
     * 新增结算消息队列
     */
	String MQ_PAY_CREATE_SETTLEMENT_QUEUE = "eyuan-pay-notice-b2c-queue";

	/**
	 * 支付通知消息队列(票务)
	 */
	String MQ_PAY_NOTICE_TICKET_SERVICE_QUEUE = "eyuan-pay-notice-ticket-service-queue";

	/**
	 * 支付通知门票模块消息路由key
	 */
	String MQ_PAY_NOTICE_TICKET_ROUTING_KEY = "eyuan-pay-notice-ticket-routing-key";

	/**
	 * 支付通知酒店模块消息路由key
	 */
	String MQ_PAY_NOTICE_HOTEL_ROUTING_KEY = "eyuan-pay-notice-hotel-routing-key";

	/**
	 * 支付通知商品模块消息路由key
	 */
	String MQ_PAY_NOTICE_B2C_ROUTING_KEY = "eyuan-pay-notice-b2c-routing-key";

	/**
	 * 支付通知票务模块消息路由key
	 */
	String MQ_PAY_NOTICE_TICKET_SERVICE_ROUTING_KEY = "eyuan-pay-notice-ticket-service-routing-key";

	/**
	 * 新增结算消息路由key
	 */
	String MQ_PAY_CREATE_SETTLEMENT_QUEUE_ROUTING_KEY = "eyuan-pay-create-settlement-routing-key";

	/**
	 * 结算审核通知消息队列
	 */
	String MQ_SETTLEMENT_AUDIT_NOTICE_QUEUE = "eyuan-settlement-audit-notice-queue";

	/**
	 * 结算审核通知消息路由key
	 */
	String MQ_SETTLEMENT_AUDIT_NOTICE_ROUTING_KEY = "eyuan-settlement-audit-notice-routing-key";


	/**
	 * 退款通知门票模块消息路由key
	 */
	String MQ_REFUND_NOTICE_TICKET_ROUTING_KEY = "eyuan-pay-refund-notice-ticket-routing-key";

	/**
	 * 退款通知酒店模块消息路由key
	 */
	String MQ_REFUND_NOTICE_HOTEL_ROUTING_KEY = "eyuan-pay-refund-notice-hotel-routing-key";

	/**
	 * 退款通知商品模块消息路由key
	 */
	String MQ_REFUND_NOTICE_B2C_ROUTING_KEY = "eyuan-pay-refund-notice-b2c-routing-key";

	/**
	 * 退款通知票务模块消息路由key
	 */
	String MQ_REFUND_NOTICE_TICKET_SERVICE_ROUTING_KEY = "eyuan-refund-notice-ticket-service-routing-key";

	/**
	 * 结算通知门票模块消息路由key
	 */
	String MQ_SETTLEMENT_NOTICE_TICKET_ROUTING_KEY = "eyuan-settlement-notice-ticket-routing-key";

	/**
	 * 结算通知酒店模块消息路由key
	 */
	String MQ_SETTLEMENT_NOTICE_HOTEL_ROUTING_KEY = "eyuan-settlement-notice-hotel-routing-key";

	/**
	 * 结算通知商品模块消息路由key
	 */
	String MQ_SETTLEMENT_NOTICE_B2C_ROUTING_KEY = "eyuan-settlement-notice-b2c-routing-key";
}
