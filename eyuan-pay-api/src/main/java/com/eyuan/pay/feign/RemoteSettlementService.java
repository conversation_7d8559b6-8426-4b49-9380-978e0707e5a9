package com.eyuan.pay.feign;

import com.dog.common.core.util.R;
import com.eyuan.pay.request.SettlementCreateRequest;
import com.eyuan.pay.request.WithdrawCreateRequest;
import com.eyuan.pay.response.SettlementCreateResponse;
import com.eyuan.pay.response.WithdrawCreateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @description 结算服务
 * @date 2020-08-04 13:37
 */
@FeignClient(contextId = "remoteSettlementService", value = "eyuan-pay-service")
public interface RemoteSettlementService {

	/**
	 * 创建结算
	 * @param request 请求类
	 * @return
	 */
	@PostMapping("/rest/pay/settlement/create")
	R<SettlementCreateResponse> create(@RequestBody SettlementCreateRequest request);
	
	/**
	 * 申请提现
	 * @param request 提现申请请求
	 * @return 提现申请结果
	 */
	@PostMapping("/rest/pay/settlement/withdraw")
	R<WithdrawCreateResponse> withdraw(@RequestBody WithdrawCreateRequest request);
}
