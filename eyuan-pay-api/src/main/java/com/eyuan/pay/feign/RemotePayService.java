package com.eyuan.pay.feign;

import com.dog.common.core.constant.SecurityConstants;
import com.dog.common.core.util.R;
import com.eyuan.pay.request.OrderPayRequest;
import com.eyuan.pay.request.OrderRefundRequest;
import com.eyuan.pay.request.PayChannelRequest;
import com.eyuan.pay.request.PayResultQueryRequest;
import com.eyuan.pay.response.OrderRefundResponse;
import com.eyuan.pay.response.PayChannelDTO;
import com.eyuan.pay.response.PayResultQueryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 订单支付服务
 * @date 2020-08-04 13:37
 */
@FeignClient(contextId = "remotePayService", value = "eyuan-pay-service")
public interface RemotePayService {

	/**
	 * 订单支付
	 * @param request 请求类
	 * @return 返回支付签名信息
	 */
	@PostMapping("/rest/pay/orderPay")
	R<Map<String, Object>> orderPay(OrderPayRequest request);

	@PostMapping("/rest/pay/test")
	R<String> test();

	/**
	 * 支付结果查询
	 * @return
	 */
	@PostMapping("/rest/pay/getPayResult")
	R<PayResultQueryResponse> getPayResult(@RequestHeader(SecurityConstants.FROM) String from, @RequestBody PayResultQueryRequest request);

	/**
	 * 退款
	 * @param request
	 * @return
	 */
	@PostMapping("/rest/pay/refund")
	R<OrderRefundResponse> refund(@RequestBody OrderRefundRequest request);


	@PostMapping("/rest/front/payChannel/list")
	R<List<PayChannelDTO>> payChannelList();

	@PostMapping("/rest/front/payChannel/get")
	R<PayChannelDTO> payChannelGet(@RequestBody @NotNull PayChannelRequest payChannelRequest);
}
