package com.eyuan.pay.enums;

/**
 * 结算审核状态枚举
 */
public enum SettlementAuditStatusEnum {
    PENDING("PENDING", "待审核"),
    APPROVED("APPROVED", "审核通过"),
    REJECTED("REJECTED", "审核拒绝");

    private final String value;
    private final String desc;

    SettlementAuditStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static SettlementAuditStatusEnum fromValue(String value) {
        for (SettlementAuditStatusEnum status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知审核状态: " + value);
    }
} 