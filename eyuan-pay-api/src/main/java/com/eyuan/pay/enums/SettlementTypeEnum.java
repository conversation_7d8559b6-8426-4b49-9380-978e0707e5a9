package com.eyuan.pay.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 结算类型
 * <AUTHOR>
 * @since 2021-11-27 15:50
 */
@AllArgsConstructor
@Getter
public enum SettlementTypeEnum {

	BANK_CARD(1, "代付（银行卡）"),
	WEIXIN(2, "微信"),
	ALIPAY(3, "支付宝"),
	BALANCE(5, "余额"),
	SETTLEMENT(6, "结算(三方结算)"),
	;

	private Integer value;
	private String label;

	public static SettlementTypeEnum getByValue(Integer value) {
		return Arrays.asList(SettlementTypeEnum.values()).stream()
				.filter(item -> item.value.equals(value))
				.findFirst().orElse(null);
	}
}
