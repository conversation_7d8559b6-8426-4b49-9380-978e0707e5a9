package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 支付模式枚举
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@AllArgsConstructor
public enum PayModeEnum implements IEnum<String> {

    /**
     * 普通商户模式
     */
    NORMAL("normal", "普通商户模式"),

    /**
     * 服务商模式
     */
    SERVICE("service", "服务商模式");

    @Getter
    private final String value;

    @Getter
    private final String label;

    @Override
    public String value() {
        return this.value;
    }

    @Override
    public String label() {
        return this.label;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static PayModeEnum getByValue(String value) {
        return Arrays.stream(PayModeEnum.values())
                .filter(item -> item.value.equals(value))
                .findFirst().orElse(null);
    }

    /**
     * 验证值是否有效
     *
     * @param value 值
     * @return 枚举
     */
    public static PayModeEnum valid(String value) {
        PayModeEnum enumObject = getByValue(value);
        if (Objects.isNull(enumObject)) {
            throw new IllegalArgumentException("参数错误, 数据(支付模式) 格式有误");
        }
        return enumObject;
    }
}
