
package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 支付类型类型
 * @date 2020-08-19 14:49
 */
public enum PayTypeEnum implements IEnum<String> {

	ALIPAY("ALIPAY", "支付宝支付"),
	WXPAY("WXPAY", "微信支付"),
	UNIONPAY("UNIONPAY", "银联支付"),
	JDPAY("JDPAY", "京东支付");
	private String value;
	private String label;

	PayTypeEnum(String value, String label) {
		this.value = value;
		this.label = label;
	}

	@Override
	public String value() {
		return this.value;
	}

	@Override
	public String label() {
		return this.label;
	}

	public static PayTypeEnum getByValue(String value) {
		return Arrays.asList(PayTypeEnum.values()).stream()
				.filter(item -> item.value.equals(value))
				.findFirst().orElse(null);
	}

	public static PayTypeEnum valid(String value) {
		PayTypeEnum enumObject = getByValue(value);
		if (Objects.isNull(enumObject)) {
			throw new IllegalArgumentException("参数错误, 数据(支付类型) 格式有误");
		}
		return enumObject;
	}
}
