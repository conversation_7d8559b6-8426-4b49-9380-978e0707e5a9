package com.eyuan.pay.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 结算交易类型
 * <AUTHOR>
 * @since 2021-11-27 15:50
 */
@AllArgsConstructor
@Getter
public enum SettlementTradeTypeEnum {

	SETTLEMENT(1, "结算"),
	WITHDRAW(2, "提现"),
	;

	private Integer value;
	private String label;

	public static SettlementTradeTypeEnum getByValue(Integer value) {
		return Arrays.asList(SettlementTradeTypeEnum.values()).stream()
				.filter(item -> item.value.equals(value))
				.findFirst().orElse(null);
	}
}
