package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易状态枚举
 * <AUTHOR>
 * @date 2024-05-15
 */
@AllArgsConstructor
@Getter
public enum PayTradeStatusEnum implements IEnum<String> {

    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    PROCESSING("PROCESSING", "处理中");
    private final String value;
    private final String label;

    @Override
    public String value() {
        return this.value;
    }

    @Override
    public String label() {
        return this.label;
    }
}