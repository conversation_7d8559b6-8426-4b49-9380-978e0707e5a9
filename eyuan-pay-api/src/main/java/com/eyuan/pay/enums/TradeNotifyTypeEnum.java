
package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 交易通知类型枚举
 * @date 2020-08-19 14:49
 */
public enum TradeNotifyTypeEnum implements IEnum<String> {

	PAY_NOTIFY("PAY_NOTIFY", "支付通知"),
	REFUND_NOTIFY("REFUND_NOTIFY", "退款通知"),
	SETTLEMENT_NOTIFY("SETTLEMENT_NOTIFY", "结算通知"),
	TRANSFER_NOTIFY("TRANSFER_NOTIFY", "代付/转账通知");
	private String value;
	private String label;

	TradeNotifyTypeEnum(String value, String label) {
		this.value = value;
		this.label = label;
	}

	@Override
	public String value() {
		return this.value;
	}

	@Override
	public String label() {
		return this.label;
	}

	public static TradeNotifyTypeEnum getByValue(String value) {
		return Arrays.asList(TradeNotifyTypeEnum.values()).stream()
				.filter(item -> item.value.equals(value))
				.findFirst().orElse(null);
	}

	public static TradeNotifyTypeEnum valid(String value) {
		TradeNotifyTypeEnum enumObject = getByValue(value);
		if (Objects.isNull(enumObject)) {
			throw new IllegalArgumentException("参数错误, 数据(交易通知类型) 格式有误");
		}
		return enumObject;
	}
}
