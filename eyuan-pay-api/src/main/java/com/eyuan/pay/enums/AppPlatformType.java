package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description App平台类型
 * @date 2020-08-25 15:06
 */
public enum AppPlatformType implements IEnum<String> {

    B2C("b2c", "商城"),
    HOME("home", "民宿"),
	TICKET("ticket", "景区门票"),
    TICKET_SERVICE("ticketService","票务")
    ;

    AppPlatformType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    private String value;
    private String label;

    @Override
    public String value() {
        return this.value;
    }

    @Override
    public String label() {
        return this.label;
    }

    public static AppPlatformType getByValue(String value) {
        return Arrays.asList(AppPlatformType.values()).stream()
                .filter(appPlatformType -> appPlatformType.value.equals(value))
                .findFirst().orElseThrow(() -> new IllegalArgumentException("参数错误, 平台类型(AppPlatformType) 格式有误"));
    }

    public static void valid(String value) {
		AppPlatformType.getByValue(value);
	}

}
