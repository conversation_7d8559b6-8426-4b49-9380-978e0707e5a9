package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;
import com.eyuan.pay.constant.PayConstants;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 支付工具类型
 * @date 2020-08-19 14:49
 */
public enum PayToolEnum implements IEnum<Integer> {

	ALIPAY_WAP(0, "支付宝wap支付",PayChannelEnum.ALIPAY.getValue(),PayTypeEnum.ALIPAY.value(),PaySceneEnum.WAP.value()),
	WEIXIN_MP(1, "微信公众号支付",PayChannelEnum.WECHAT_PAY.getValue(),PayTypeEnum.WXPAY.value(),PaySceneEnum.APPLET.value()),
	WEIXIN_WAP(6, "微信H5支付",PayChannelEnum.WECHAT_PAY.getValue(),PayTypeEnum.WXPAY.value(),PaySceneEnum.WAP.value()),
	WEIXIN_PAYMENT_CODE(2, "微信付款码支付",PayChannelEnum.WECHAT_PAY.getValue(),PayTypeEnum.WXPAY.value(),PaySceneEnum.SWIPE.value()),
	WEIXIN_NATIVE(7, "微信扫码支付",PayChannelEnum.WECHAT_PAY.getValue(),PayTypeEnum.WXPAY.value(),PaySceneEnum.SCAN.value()),
	ALIPAY_PAYMENT_CODE(3, "支付宝付款码支付",PayChannelEnum.ALIPAY.getValue(),PayTypeEnum.ALIPAY.value(),PaySceneEnum.SWIPE.value()),
	ALIPAY_MP(8, "支付宝付小程序支付",PayChannelEnum.ALIPAY.getValue(),PayTypeEnum.ALIPAY.value(),PaySceneEnum.APPLET.value()),
	HELIPAY_WEIXIN_APPLET(5, "合利宝微信小程序支付",PayChannelEnum.HELIPAY.getValue(),PayTypeEnum.WXPAY.value(),PaySceneEnum.APPLET.value()),
	HELIPAY_WEIXIN_MP(9, "合利宝微信公众号支付",PayChannelEnum.HELIPAY.getValue(),PayTypeEnum.WXPAY.value(),PaySceneEnum.PUBLIC.value()),
	HELIPAY_WEIXIN_WAP(10, "合利宝微信H5支付",PayChannelEnum.HELIPAY.getValue(),PayTypeEnum.WXPAY.value(),PaySceneEnum.WAP.value());

	private Integer value;
	private String label;
	private String payChanelValue;
	private String payTypeValue;
	private String paySceneValue;

	PayToolEnum(Integer value, String label,String payChanelValue,String payTypeValue,String paySceneValue) {
		this.value = value;
		this.label = label;
		this.payChanelValue = payChanelValue;
		this.payTypeValue = payTypeValue;
		this.paySceneValue = paySceneValue;
	}

	@Override
	public Integer value() {
		return this.value;
	}

	@Override
	public String label() {
		return this.label;
	}

	public String payChanelValue() {
		return payChanelValue;
	}

	public String payTypeValue() {
		return payTypeValue;
	}

	public String paySceneValue() {
		return paySceneValue;
	}

	public static PayToolEnum getByValue(Integer value) {
		return Arrays.asList(PayToolEnum.values()).stream()
				.filter(item -> item.value.equals(value))
				.findFirst().orElse(null);
	}

	public static PayToolEnum valid(Integer value) {
		PayToolEnum enumObject = getByValue(value);
		if (Objects.isNull(enumObject)) {
			throw new IllegalArgumentException("参数错误, 数据(支付渠道) 格式有误");
		}
		return enumObject;
	}

	/**
	 * 通过ua 判断所属渠道
	 *
	 * @param ua 浏览器类型
	 * @return
	 */
	public static PayToolEnum getChannel(String ua) {
		if (ua.contains(PayConstants.ALIPAY)) {
			return PayToolEnum.ALIPAY_WAP;
		} else if (ua.contains(PayConstants.MICRO_MESSENGER)) {
			return PayToolEnum.WEIXIN_MP;
		} else {
			return PayToolEnum.WEIXIN_WAP;
		}
	}
}
