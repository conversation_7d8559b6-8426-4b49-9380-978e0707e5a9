package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 支付场景枚举
 * @date 2020-08-19 14:49
 */
public enum PaySceneEnum implements IEnum<String> {

	APPLET("APPLET", "小程序支付"),
	WAP("WAP", "WAP(H5)支付"),
	PUBLIC("PUBLIC", "公众号支付"),
	SCAN("SCAN", "扫码（主扫）支付"),
	APP("APP", "APP支付"),
	SWIPE("SWIPE", "刷卡(被扫)");

	private String value;
	private String label;

	PaySceneEnum(String value, String label) {
		this.value = value;
		this.label = label;
	}

	@Override
	public String value() {
		return this.value;
	}

	@Override
	public String label() {
		return this.label;
	}

	public static PaySceneEnum getByValue(String value) {
		return Arrays.stream(PaySceneEnum.values())
				.filter(item -> item.value.equals(value))
				.findFirst().orElse(null);
	}

	public static PaySceneEnum valid(String value) {
		PaySceneEnum enumObject = getByValue(value);
		if (Objects.isNull(enumObject)) {
			throw new IllegalArgumentException("参数错误, 数据(支付场景) 格式有误");
		}
		return enumObject;
	}
}
