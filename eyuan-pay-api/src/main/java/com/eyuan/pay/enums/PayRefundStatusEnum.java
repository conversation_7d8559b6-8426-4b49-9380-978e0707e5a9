package com.eyuan.pay.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 退款状态:0-订单生成,1-退款中,2-退款成功,3-退款失败,4-业务处理完成
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-27 15:50
 */
@AllArgsConstructor
@Getter
public enum PayRefundStatusEnum {

	INIT("0", "订单生成"),
	REFUNDING("1", "退款中"),
	REFUND_SUCCESS("2", "退款成功"),
	REFUND_FAILURE("3", "退款失败")
	;

	private String value;
	private String label;
}
