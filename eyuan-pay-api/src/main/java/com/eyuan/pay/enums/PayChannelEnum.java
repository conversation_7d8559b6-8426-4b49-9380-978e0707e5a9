package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 支付渠道枚举
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@AllArgsConstructor
public enum PayChannelEnum implements IEnum<String> {

    /**
     * 微信支付
     */
    WECHAT_PAY("WECHAT_PAY", "微信支付"),

    /**
     * 支付宝
     */
    ALIPAY("ALIPAY", "支付宝"),

    /**
     * 合利宝
     */
    HELIPAY("HELIPAY", "合利宝");

    @Getter
    private final String value;

    @Getter
    private final String label;

    @Override
    public String value() {
        return this.value;
    }

    @Override
    public String label() {
        return this.label;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static PayChannelEnum getByValue(String value) {
        return Arrays.stream(PayChannelEnum.values())
                .filter(item -> item.value.equals(value))
                .findFirst().orElse(null);
    }

    /**
     * 验证值是否有效
     *
     * @param value 值
     * @return 枚举
     */
    public static PayChannelEnum valid(String value) {
        PayChannelEnum enumObject = getByValue(value);
        if (Objects.isNull(enumObject)) {
            throw new IllegalArgumentException("参数错误, 数据(支付渠道) 格式有误");
        }
        return enumObject;
    }
}
