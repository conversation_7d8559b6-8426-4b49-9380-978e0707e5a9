package com.eyuan.pay.enums;

import com.dog.common.core.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 提现状态枚举
 * <AUTHOR>
 * @date 2024-05-15
 */
@AllArgsConstructor
@Getter
public enum WithdrawStatusEnum implements IEnum<Integer> {

    PENDING(0, "审核中"),
    APPROVED(1, "审核通过"),
    REJECTED(2, "审核拒绝"),
    PROCESSING(3, "打款中"),
    SUCCESS(4, "打款成功"),
    FAILED(5, "打款失败");

    private final Integer value;
    private final String label;

    @Override
    public Integer value() {
        return this.value;
    }

    @Override
    public String label() {
        return this.label;
    }

    /**
     * 根据值获取枚举
     * @param value 值
     * @return 枚举
     */
    public static WithdrawStatusEnum getByValue(Integer value) {
        return Arrays.stream(WithdrawStatusEnum.values())
                .filter(item -> item.value.equals(value))
                .findFirst().orElse(null);
    }
}