package com.eyuan.pay.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 微信扫码支付回调(模式一)返回
 * @date 2020-10-31 15:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxNativePayCallbackResponse implements java.io.Serializable {

	private static final long serialVersionUID = -7982967463108312875L;

//	返回状态码	return_code	String(16)	是	SUCCESS	SUCCESS/FAIL,此字段是通信标识，非交易标识，交易是否成功需要查看result_code来判断
	private String return_code;

//	返回信息	return_msg	String(128)	否	签名失败	返回信息，如非空，为错误原因;签名失败;具体某个参数格式校验错误.
	private String return_msg;

//	公众账号ID	appid	String(32)	是	wx8888888888888888	微信分配的公众账号ID
	private String appid;

//	商户号	mch_id	String(32)	是	1900000109	微信支付分配的商户号
	private String mch_id;

//	随机字符串	nonce_str	String(32)	是	5K8264ILTKCH16CQ2502SI8ZNMTM67VS	微信返回的随机字符串
	private String nonce_str;

//	预支付ID	prepay_id	String(64)	是	wx201410272009395522657a690389285100	调用统一下单接口生成的预支付ID
	private String prepay_id;

//	业务结果	result_code	String(16)	是	SUCCESS	SUCCESS/FAIL
	private String result_code;

//	错误描述	err_code_des	String(128)	否		当result_code为FAIL时，商户展示给用户的错误提
	private String err_code_des;

//	签名	sign	String(32)	是	C380BEC2BFD727A4B6845133519F3AD6	返回数据签名，签名生成算法
	private String sign;
}
