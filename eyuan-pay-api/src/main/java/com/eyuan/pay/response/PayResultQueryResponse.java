package com.eyuan.pay.response;

import com.dog.common.core.annotation.IEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 支付结果查询返回
 * @date 2020-08-04 14:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(description = "支付结果返回")
@Builder
public class PayResultQueryResponse implements java.io.Serializable {

	private static final long serialVersionUID = -5474081353554796748L;

	/**
	 * {@link PayResultStatus}
	 */
	@ApiModelProperty(value="支付结果状态 [success:支付成功; failure:支付失败; doing:支付中;]")
	private String status;

	//  [格式: yyyy-MM-dd HH:mm:ss]
	@ApiModelProperty(value="支付时间")
	private Date payTime;

	@ApiModelProperty(value="支付订单号 即: 业务平台订单号")
	private String payOrderId;
	/**
	 * 渠道订单号
	 */
	@ApiModelProperty(value="渠道订单号 即：三方支付订单号")
	private String channelOrderNo;

	@ApiModelProperty(value="支付金额 单位:分")
	private String payAmt;

	@ApiModelProperty(value="支付工具类型 [0:支付宝; 1:微信]")
	private String payTool;



	public enum PayResultStatus implements IEnum<String> {

		SUCCESS("success", "支付成功"),
		FAILURE("failure", "支付失败"),
		DOING("doing", "支付中")
		;

		private String value;
		private String label;

		PayResultStatus(String value, String label) {
			this.value = value;
			this.label = label;
		}

		@Override
		public String value() {
			return this.value;
		}

		@Override
		public String label() {
			return this.label;
		}
	}
}
