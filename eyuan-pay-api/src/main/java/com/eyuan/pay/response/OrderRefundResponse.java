package com.eyuan.pay.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @description 订单响应参数
 * @date 2020-08-04 14:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString(callSuper = true)
@ApiModel(description = "订单响应参数")
public class OrderRefundResponse implements java.io.Serializable {

	private static final long serialVersionUID = 8243505500228982600L;

	@ApiModelProperty(value="支付订单号")
	private String payOrderId;

	@ApiModelProperty(value="商户退款单号(商城填退款批次号)")
	private String mchRefundNo;

	@ApiModelProperty(value="外部退款单号")
	private String outRefundNo;

	//返回状态
	@ApiModelProperty(value="返回状态")
	private String returnStatus;
}
