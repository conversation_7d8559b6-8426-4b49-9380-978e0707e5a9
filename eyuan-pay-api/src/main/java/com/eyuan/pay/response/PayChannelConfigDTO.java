package com.eyuan.pay.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付渠道配置DTO
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@Data
public class PayChannelConfigDTO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 渠道标识，如WECHAT_PAY、ALIPAY、HELIPAY
     */
    private String channelId;

    /**
     * 渠道名称，如微信支付、支付宝、合利宝
     */
    private String channelName;

    /**
     * 商户号
     */
    private String merchantId;

    /**
     * 商户密钥
     */
    private String merchantKey;

    /**
     * 子商户号，用于微信服务商模式
     */
    private String subMerchantId;

    /**
     * 支付模式，normal-普通商户模式，service-服务商模式
     */
    private String mode;

    /**
     * 渠道参数，JSON格式存储特定配置
     */
    private String param;

    /**
     * 状态，1-启用，0-禁用
     */
    private String state;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
