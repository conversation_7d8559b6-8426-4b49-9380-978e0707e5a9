package com.eyuan.pay.response;

import lombok.Data;

/**
 * 渠道
 *
 * <AUTHOR>
 * @date 2019-05-28 23:57:58
 */
@Data
public class PayChannelDTO implements java.io.Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 应用ID
	 */
	private String appId;
	/**
	 * 渠道ID
	 * @see com.eyuan.pay.enums.PayChannelEnum
	 */
	private String channelId;

	/**
	 * 支付工具（支付需要传的值）
	 *
	 * @see com.eyuan.pay.enums.PayToolEnum
	 */
	private Integer payTool;

	/**
     * 支付类型,如:ALIPAY,WXPAY,UNIONPAY
	 * @see com.eyuan.pay.enums.PayTypeEnum
     */
	private String payType;

	/**
	 * 支付场景
	 * @see com.eyuan.pay.enums.PaySceneEnum
	 */
	private String payScene;
}
