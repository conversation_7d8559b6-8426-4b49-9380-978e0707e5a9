package com.eyuan.pay.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算响应参数
 * @Author: sunwh
 * @Date: 2025/5/17 16:32
 */
@Data
public class SettlementCreateResponse { /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;

    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("结算到哪")
    private String settleTo;

    @ApiModelProperty("账号")
    private String accountNo;

    @ApiModelProperty("结算时间")
    private LocalDateTime settlementTime;

}
