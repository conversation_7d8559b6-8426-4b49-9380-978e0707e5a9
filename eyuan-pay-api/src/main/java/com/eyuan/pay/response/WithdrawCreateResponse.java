package com.eyuan.pay.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现申请响应
 * <AUTHOR>
 * @date 2024-05-15
 */
@Data
@ApiModel("提现申请响应")
public class WithdrawCreateResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提现申请ID
     */
    @ApiModelProperty(value="提现申请ID")
    private String id;
    
    /**
     * 提现单号
     */
    @ApiModelProperty(value="提现单号")
    private String withdrawNo;

    /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;
    
    /**
     * 提现金额
     */
    @ApiModelProperty(value="提现金额")
    private BigDecimal amount;
    
    /**
     * 提现状态(0:审核中,1:审核通过,2:审核拒绝,3:打款中,4:打款成功,5:打款失败)
     */
    @ApiModelProperty(value="提现状态")
    private Integer status;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;
}