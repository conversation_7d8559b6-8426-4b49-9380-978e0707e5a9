<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.eyuan</groupId>
        <artifactId>eyuan-pay</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>eyuan-pay-api</artifactId>
    <dependencies>
        <!--swagger 依赖-->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <!--feign 工具类-->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-feign</artifactId>
        </dependency>
    </dependencies>
</project>