<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cloud</groupId>
        <artifactId>dog</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.eyuan</groupId>
    <artifactId>eyuan-pay</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>eyuan-pay-api</module>
        <module>eyuan-pay-service</module>
    </modules>

    <properties>
        <!--<java.version>1.8</java.version>-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <dog.version>1.0.0</dog.version>
    </properties>

</project>