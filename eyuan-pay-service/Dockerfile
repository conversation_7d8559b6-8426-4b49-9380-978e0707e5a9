FROM openjdk:8

MAINTAINER <EMAIL>

ENV TZ=Asia/Shanghai

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN mkdir -p /root/cert

#WORKDIR /root/cert

#COPY /root/cert/apiclient_cert.p12 /root/cert

RUN mkdir -p /eyuan-pay-service

WORKDIR /eyuan-pay-service

EXPOSE 5010

ADD ./target/eyuan-pay-service.jar ./

CMD sleep 10;java -Xms128m -Xmx256m -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8888 -Djava.security.egd=file:/dev/./urandom -jar eyuan-pay-service.jar
