<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eyuan.pay.settlement.mapper.WithdrawApplicationMapper">

    <select id="pageWithdraws" resultType="com.eyuan.pay.settlement.entity.WithdrawApplication">
        SELECT
            id,
            withdraw_no,
            account_type,
            account_id,
            supplier_id,
            distributor_id,
            amount,
            bank_card_id,
            status,
            audit_user_id,
            audit_time,
            audit_remark,
            payment_time,
            payment_remark,
            create_time,
            update_time,
            create_by,
            update_by,
            deleted,
            tenant_id
        FROM
            withdraw_application
        WHERE
            deleted = 'F'
            <if test="supplierId != null">
                AND supplier_id = #{supplierId}
            </if>
            <if test="distributorId != null">
                AND distributor_id = #{distributorId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="accountType != null">
                AND account_type = #{accountType}
            </if>
    </select>

</mapper>