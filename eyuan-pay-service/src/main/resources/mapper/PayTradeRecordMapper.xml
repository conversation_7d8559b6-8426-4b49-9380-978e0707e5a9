<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eyuan.pay.mapper.PayTradeRecordMapper">

    <resultMap id="payTradeRecordMap" type="com.eyuan.pay.entity.PayTradeRecord">
        <id property="id" column="id"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="thirdTradeNo" column="third_trade_no"/>
        <result property="tradeType" column="trade_type"/>
        <result property="request" column="request"/>
        <result property="response" column="response"/>
        <result property="orderNo" column="order_no"/>
        <result property="status" column="status"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    
    <select id="getByTradeNo" resultMap="payTradeRecordMap">
        SELECT *
        FROM pay_trade_record
        WHERE deleted = 'F'
        AND trade_no = #{tradeNo}
        LIMIT 1
    </select>
    
    <select id="findByThirdTradeNo" resultMap="payTradeRecordMap">
        SELECT *
        FROM pay_trade_record
        WHERE deleted = 'F'
        AND third_trade_no = #{thirdTradeNo}
    </select>
    
    <select id="findByOrderNo" resultMap="payTradeRecordMap">
        SELECT *
        FROM pay_trade_record
        WHERE deleted = 'F'
        AND order_no = #{orderNo}
    </select>
</mapper>