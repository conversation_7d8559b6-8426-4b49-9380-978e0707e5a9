<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eyuan.pay.mapper.PayGoodsOrderMapper">

	<resultMap id="payGoodsOrderMap" type="com.eyuan.pay.entity.PayGoodsOrder">
		<id property="goodsOrderId" column="goods_order_id"/>
		<result property="goodsId" column="goods_id"/>
		<result property="goodsName" column="goods_name"/>
		<result property="amount" column="amount"/>
		<result property="userId" column="user_id"/>
		<result property="status" column="status"/>
		<result property="payOrderId" column="pay_order_id"/>
		<result property="deleted" column="deleted"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="tenantId" column="tenant_id"/>
	</resultMap>

	<select id="getByPayOrderId" resultType="com.eyuan.pay.entity.PayGoodsOrder">
		select *
		from pay_goods_order
		where deleted = 'F' and pay_order_id = #{payOrderId}
	</select>

</mapper>
