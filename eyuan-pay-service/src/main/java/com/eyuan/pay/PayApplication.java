package com.eyuan.pay;

import com.dog.common.security.annotation.EnableDogResourceServer;
import com.dog.common.swagger.annotation.EnableDogSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@EnableDogSwagger
@SpringBootApplication
@EnableDogResourceServer
@ComponentScan(basePackages = {"com.eyuan","com.dog"})
@EnableFeignClients(basePackages = {"com.dog","com.eyuan"})
public class PayApplication {

    public static void main(String[] args) {
      SpringApplication.run(PayApplication.class, args);
    }

}
