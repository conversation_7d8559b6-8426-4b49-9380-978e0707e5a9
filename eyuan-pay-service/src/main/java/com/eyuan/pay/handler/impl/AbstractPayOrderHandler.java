package com.eyuan.pay.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dog.common.sequence.sequence.Sequence;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.handler.PayOrderHandler;
import com.eyuan.pay.mapper.PayGoodsOrderMapper;
import com.eyuan.pay.mapper.PayTradeOrderMapper;
import com.eyuan.pay.service.PayChannelConfigService;
import com.eyuan.pay.utils.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-31
 */
@Slf4j
public abstract class AbstractPayOrderHandler<PayConfig> implements PayOrderHandler {
	@Autowired
	private PayGoodsOrderMapper goodsOrderMapper;
	@Autowired
	private Sequence paySequence;
	@Resource
	protected PayTradeOrderMapper tradeOrderMapper;
	@Resource
	private PayChannelConfigService payChannelConfigService;

	/**
	 * 创建商品订单
	 *
	 * @param goodsOrder 商品订单
	 * @return
	 */
	public void createGoodsOrder(PayGoodsOrder goodsOrder) {
		if (StringUtils.isAllBlank(goodsOrder.getPayOrderId())) {
			goodsOrder.setPayOrderId(paySequence.nextNo());
		}
		List<PayGoodsOrder> payGoodsOrders = goodsOrderMapper.selectList(new LambdaQueryWrapper<PayGoodsOrder>().eq(PayGoodsOrder::getPayOrderId, goodsOrder.getPayOrderId()));
		if (CollectionUtil.isEmpty(payGoodsOrders)) {
			goodsOrder.setStatus(OrderStatusEnum.INIT.getStatus());
			goodsOrder.setBizOrderCode(goodsOrder.getPayOrderId());
			goodsOrder.setPlatformType(goodsOrder.getPlatformType());
			goodsOrder.setTenantId(null);
			goodsOrderMapper.insert(goodsOrder);
		} else {
			log.info("商品订单存在，不需要插入");
			PayGoodsOrder payGoodsOrder = payGoodsOrders.get(0);
			BeanUtil.copyProperties(payGoodsOrder, goodsOrder);
		}

	}

	/**
	 * 调用入口
	 *
	 * @return
	 */
	@Override
	public Object handle(PayGoodsOrder payGoodsOrder) {
		createGoodsOrder(payGoodsOrder);
		PaySceneDTO paySceneDTO = getPaySceneConfig();
		PayConfig payConfig = convertConfig(paySceneDTO);

		PayTradeOrder tradeOrder = createTradeOrder(payGoodsOrder,payConfig);
		Object result = pay(payGoodsOrder, tradeOrder,payConfig);
		updateOrder(payGoodsOrder, tradeOrder);
		return result;
	}

	private void updateOrder(PayGoodsOrder payGoodsOrder, PayTradeOrder tradeOrder) {
		tradeOrderMapper.updateById(tradeOrder);
		goodsOrderMapper.updateById(payGoodsOrder);
	}

	protected abstract Object pay(PayGoodsOrder payGoodsOrder, PayTradeOrder tradeOrder, PayConfig payConfig);

	protected abstract PayTradeOrder createTradeOrder(PayGoodsOrder payGoodsOrder, PayConfig payConfig);

	protected abstract PayConfig convertConfig(PaySceneDTO paySceneDTO);


	private PaySceneDTO getPaySceneConfig() {
		PayToolEnum payToolEnum = PayToolEnum.getByValue(payChannel());
		return payChannelConfigService.getPaySceneConfig(payToolEnum.payChanelValue(), payToolEnum.payTypeValue(), payToolEnum.paySceneValue());
	}
}
