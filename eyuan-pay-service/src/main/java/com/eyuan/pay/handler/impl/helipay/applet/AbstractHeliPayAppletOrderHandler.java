package com.eyuan.pay.handler.impl.helipay.applet;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.handler.impl.helipay.AbstractHeliPayOrderHandler;
import com.eyuan.pay.helipay.client.HeliAppPayClient;
import com.eyuan.pay.helipay.constant.HeliPayConstant;
import com.eyuan.pay.helipay.enums.BizTypeEnum;
import com.eyuan.pay.helipay.enums.PayType;
import com.eyuan.pay.helipay.request.AppPayAppletOrderRequest;
import com.eyuan.pay.helipay.response.AppPayAppletOrderResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.helipay.util.HttpUtils;
import com.eyuan.pay.mapper.PayTradeOrderMapper;
import com.eyuan.pay.utils.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * <p>
 * 合利宝微信小程序支付
 */
@Slf4j
public abstract class AbstractHeliPayAppletOrderHandler extends AbstractHeliPayOrderHandler {
	@Resource
	private PayTradeOrderMapper tradeOrderMapper;
	@Resource
	private HttpServletRequest request;
	@Resource
	private HeliAppPayClient heliAppPayClient;
	@Resource
	private PayCommonProperties payCommonProperties;

	/**
	 * 创建交易订单
	 *
	 * @param goodsOrder
	 * @return
	 */
	@Override
	public PayTradeOrder createTradeOrder(PayGoodsOrder goodsOrder,PaySceneDTO paySceneDTO) {

		PayTradeOrder payTradeOrder = tradeOrderMapper.selectOne(new LambdaQueryWrapper<PayTradeOrder>().eq(PayTradeOrder::getOrderId, goodsOrder.getPayOrderId()));
		if (Objects.nonNull(payTradeOrder)) {
			log.info("[支付] 微信, 重复交易订单, payOrderId:{}", goodsOrder.getPayOrderId());
			return payTradeOrder;
		}
		PayTradeOrder tradeOrder = new PayTradeOrder();
		tradeOrder.setOrderId(goodsOrder.getPayOrderId());
		tradeOrder.setAmount(goodsOrder.getAmount());
		tradeOrder.setChannelId(getPayToolEnum().name());
		tradeOrder.setChannelMchId(paySceneDTO.getMerchantId());
		tradeOrder.setClientIp(ServletUtil.getClientIP(request));
		tradeOrder.setCurrency("CNY");
		tradeOrder.setStatus(OrderStatusEnum.INIT.getStatus());
		tradeOrder.setBody(goodsOrder.getGoodsName());
		tradeOrderMapper.insert(tradeOrder);
		return tradeOrder;
	}

	/**
	 * 调起渠道支付
	 *
	 * @param goodsOrder 商品订单
	 * @param tradeOrder 交易订单
	 */
	@Override
	public Object pay(PayGoodsOrder goodsOrder, PayTradeOrder tradeOrder, PaySceneDTO paySceneDTO) {
		String ip = ServletUtil.getClientIP(request);
		String appId = paySceneDTO.getAppId();
		AppPayAppletOrderRequest appPayAppletOrderRequest = new AppPayAppletOrderRequest();
		appPayAppletOrderRequest.setP1_bizType(BizTypeEnum.AppPayApplet.name());
		appPayAppletOrderRequest.setP2_orderId(tradeOrder.getOrderId());
		appPayAppletOrderRequest.setP3_customerNumber(paySceneDTO.getMerchantId());
		appPayAppletOrderRequest.setP4_payType(PayType.APPLET.name());
		appPayAppletOrderRequest.setP5_appid(appId);
		appPayAppletOrderRequest.setP8_openid(goodsOrder.getUserId());
		
		BigDecimal amount = new BigDecimal(tradeOrder.getAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
		appPayAppletOrderRequest.setP9_orderAmount(amount.toString());
		appPayAppletOrderRequest.setP10_currency("CNY");
		appPayAppletOrderRequest.setP11_appType(getAppPayType());
		appPayAppletOrderRequest.setP12_notifyUrl(payCommonProperties.getHeliPayConfig().getPayNotifyUrl());
		appPayAppletOrderRequest.setP14_orderIp(ip);
		appPayAppletOrderRequest.setP15_goodsName(goodsOrder.getGoodsName());
		log.info("[订单支付-请求微信 - 统一收单] 请求appId:{}, params:{}", appId, JSON.toJSONString(appPayAppletOrderRequest));
//        Map<String,String> map = null;
//        try {
//            map = HeliBeanUtils.convertBean(appPayAppletOrderRequest,new LinkedHashMap<>());
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException(e);
//        }
//        String sign = HeliSignatureUtils.getSignAndEncryptedByReq(map, AppPayAppletOrderRequest.NEED_SIGN_PARAMS,
//				AppPayAppletOrderRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
//		map.put("sign", sign);
		log.info("[订单支付-请求合利宝-小程序支付 - 统一收单] 请求appId:{} params:{}", appId,JSONUtil.toJsonStr(appPayAppletOrderRequest));
		Map<String, String> map = HeliSignatureUtils.convertRequestAndCreateSign(appPayAppletOrderRequest,
				AppPayAppletOrderRequest.NEED_SIGN_PARAMS, AppPayAppletOrderRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
//		AppPayAppletOrderResponse response = heliAppPayClient.appPayAppletCreate(payCommonProperties.getHeliPayConfig().getPayUrl(),map);
		String result = HttpUtils.doExecute(payCommonProperties.getHeliPayConfig().getPayUrl(), map);
		log.info("[订单支付-请求合利宝-小程序支付 - 统一收单] 返回:{}", result);
		AppPayAppletOrderResponse appPayAppletOrderResponse = JSONUtil.toBean(result, AppPayAppletOrderResponse.class);
		AssertUtil.isTrue(HeliPayConstant.SUCCESS_RETURN_CODE.equals(appPayAppletOrderResponse.getRt2_retCode()), appPayAppletOrderResponse.getRt3_retMsg());
		return prepayIdCreateSign(appPayAppletOrderResponse.getRt10_payInfo());
	}

	public static Map<String, String> prepayIdCreateSign(String payInfo) {
		JSONObject jsonObject = JSONUtil.parseObj(payInfo);
		Map<String, String> packageParams = new HashMap();
		packageParams.put("appId", jsonObject.getStr("appId"));
		packageParams.put("timeStamp", jsonObject.getStr("timeStamp"));
		packageParams.put("nonceStr", jsonObject.getStr("nonceStr"));
		packageParams.put("package", jsonObject.getStr("package"));
		packageParams.put("signType", jsonObject.getStr("signType"));
//		String packageSign = PaymentKit.createSign(packageParams, paternerKey);
		packageParams.put("paySign", jsonObject.getStr("paySign"));
		return packageParams;
	}
}
