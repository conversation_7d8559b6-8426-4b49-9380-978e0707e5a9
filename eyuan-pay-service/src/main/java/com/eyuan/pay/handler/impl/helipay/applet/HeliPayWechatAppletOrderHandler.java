package com.eyuan.pay.handler.impl.helipay.applet;

import com.eyuan.pay.enums.PayToolEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * <p>
 * 合利宝微信小程序支付
 */
@Service
public class HeliPayWechatAppletOrderHandler extends AbstractHeliPayAppletOrderHandler {
	@Override
	public Integer payChannel() {
		return PayToolEnum.HELIPAY_WEIXIN_APPLET.value();
	}

	@Override
	protected PayToolEnum getPayToolEnum() {
		return PayToolEnum.HELIPAY_WEIXIN_APPLET;
	}
}
