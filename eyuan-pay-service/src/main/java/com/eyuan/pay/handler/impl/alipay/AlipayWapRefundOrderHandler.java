package com.eyuan.pay.handler.impl.alipay;

import cn.hutool.json.JSONUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.dog.common.core.exception.BusinessException;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.constant.PayRefundParams;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayRefundOrder;
import com.eyuan.pay.enums.PayChannelEnum;
import com.eyuan.pay.enums.PayRefundStatusEnum;
import com.eyuan.pay.handler.impl.AbstractRefundOrderHandler;
import com.eyuan.pay.response.OrderRefundResponse;
import com.eyuan.pay.utils.PayApiConfigBuildUtil;
import com.ijpay.alipay.AliPayApi;
import com.ijpay.alipay.AliPayApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 支付退款
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-27 14:59
 */
@Slf4j
@Service
public class AlipayWapRefundOrderHandler extends AbstractRefundOrderHandler {

	@Override
	public OrderRefundResponse refund(PayRefundOrder payRefundOrder,PaySceneDTO paySceneDTO) {
		AlipayTradeRefundModel alipayTradeRefundModel = new AlipayTradeRefundModel();
		AlipayTradeRefundResponse alipayTradeRefundResponse;
		boolean result;
		try {
			// 商户订单号。 订单支付时传入的商户订单号，商家自定义且保证商家系统中唯一。与支付宝交易号 trade_no 不能同时为空。
			alipayTradeRefundModel.setOutTradeNo(payRefundOrder.getPayOrderId());
			BigDecimal refundAmount = new BigDecimal(payRefundOrder.getRefundAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
			alipayTradeRefundModel.setRefundAmount(String.valueOf(refundAmount));
			alipayTradeRefundModel.setOutRequestNo(String.valueOf(payRefundOrder.getMchRefundNo()));
			log.info("[退款] 支付宝, request:{}", JSONUtil.toJsonStr(alipayTradeRefundModel));
			AliPayApiConfig aliPayApiConfig = PayApiConfigBuildUtil.buildAliPayApiConfig(paySceneDTO);
			alipayTradeRefundResponse = AliPayApi.tradeRefundToResponse(aliPayApiConfig.getAliPayClient(), true, alipayTradeRefundModel);
			log.info("[退款] 支付宝, response:{}", JSONUtil.toJsonStr(alipayTradeRefundResponse));
			AssertUtil.notNull(alipayTradeRefundResponse, "三方服务(alipay)返回处理结果为空");
			result = PayRefundParams.REFUND_SUCCESS_CODE.equals(alipayTradeRefundResponse.getCode());
		} catch (AlipayApiException e) {
			log.info("[退款] 支付宝, 请求三方服务退款失败, e:", e);
			throw new BusinessException("三方服务(alipay)退款失败");
		}
//		AssertUtil.isTrue(result, "三方服务退款失败:" + alipayTradeRefundResponse.getSubMsg());
		// 渠道退款订单号，支付宝交易号
		payRefundOrder.setChannelOrderNo(alipayTradeRefundResponse.getTradeNo());
		payRefundOrder.setChannelErrCode(alipayTradeRefundResponse.getCode());
		payRefundOrder.setChannelErrMsg(alipayTradeRefundResponse.getMsg());
		payRefundOrder.setExtra(JSONUtil.toJsonStr(alipayTradeRefundResponse));
		if (result) {
			payRefundOrder.setStatus(PayRefundStatusEnum.REFUND_SUCCESS.getValue());
		} else {
			payRefundOrder.setStatus(PayRefundStatusEnum.REFUND_FAILURE.getValue());
		}
		return OrderRefundResponse.builder().payOrderId(payRefundOrder.getPayOrderId()).mchRefundNo(payRefundOrder.getMchRefundNo())
				.outRefundNo(alipayTradeRefundResponse.getOutTradeNo()).build();
	}

	@Override
	public String payChannel() {
		return PayChannelEnum.ALIPAY.value();
	}
}
