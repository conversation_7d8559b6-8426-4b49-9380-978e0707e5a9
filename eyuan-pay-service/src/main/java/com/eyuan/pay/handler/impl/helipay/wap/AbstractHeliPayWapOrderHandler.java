package com.eyuan.pay.handler.impl.helipay.wap;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.handler.impl.helipay.AbstractHeliPayOrderHandler;
import com.eyuan.pay.helipay.client.HeliAppPayClient;
import com.eyuan.pay.helipay.constant.HeliPayConstant;
import com.eyuan.pay.helipay.enums.BizTypeEnum;
import com.eyuan.pay.helipay.enums.PayType;
import com.eyuan.pay.helipay.request.AppPayPublicOrderRequest;
import com.eyuan.pay.helipay.request.AppPayWapCreateOrderRequest;
import com.eyuan.pay.helipay.response.AppPayWapCreateOrderResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.helipay.util.HttpUtils;
import com.eyuan.pay.mapper.PayTradeOrderMapper;
import com.eyuan.pay.utils.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * <p>
 * 合利宝WAP(h5)支付
 */
@Slf4j
public abstract class AbstractHeliPayWapOrderHandler extends AbstractHeliPayOrderHandler {
	@Resource
	private PayTradeOrderMapper tradeOrderMapper;
	@Resource
	private HttpServletRequest request;
	@Resource
	private HeliAppPayClient heliAppPayClient;
	@Resource
	private PayCommonProperties payCommonProperties;

	/**
	 * 创建交易订单
	 *
	 * @param goodsOrder
	 * @return
	 */
	@Override
	public PayTradeOrder createTradeOrder(PayGoodsOrder goodsOrder,PaySceneDTO paySceneDTO) {

		PayTradeOrder payTradeOrder = tradeOrderMapper.selectOne(new LambdaQueryWrapper<PayTradeOrder>().eq(PayTradeOrder::getOrderId, goodsOrder.getPayOrderId()));
		if (Objects.nonNull(payTradeOrder)) {
			log.info("[支付] 微信, 重复交易订单, payOrderId:{}", goodsOrder.getPayOrderId());
			return payTradeOrder;
		}
		PayTradeOrder tradeOrder = new PayTradeOrder();
		tradeOrder.setOrderId(goodsOrder.getPayOrderId());
		tradeOrder.setAmount(goodsOrder.getAmount());
		tradeOrder.setChannelId(getPayToolEnum().name());
		tradeOrder.setChannelMchId(paySceneDTO.getMerchantId());
		tradeOrder.setClientIp(ServletUtil.getClientIP(request));
		tradeOrder.setCurrency("CNY");
		tradeOrder.setStatus(OrderStatusEnum.INIT.getStatus());
		tradeOrder.setBody(goodsOrder.getGoodsName());
		tradeOrderMapper.insert(tradeOrder);
		return tradeOrder;
	}

	/**
	 * 调起渠道支付
	 *
	 * @param goodsOrder 商品订单
	 * @param tradeOrder 交易订单
	 */
	@Override
	public Object pay(PayGoodsOrder goodsOrder, PayTradeOrder tradeOrder, PaySceneDTO paySceneDTO) {
		String ip = ServletUtil.getClientIP(request);
		String appId = paySceneDTO.getAppId();
		AppPayWapCreateOrderRequest appPayWapCreateOrderRequest = new AppPayWapCreateOrderRequest();
		appPayWapCreateOrderRequest.setP1_bizType(BizTypeEnum.AppPayH5WFT.name());
		appPayWapCreateOrderRequest.setP2_orderId(tradeOrder.getOrderId());
		appPayWapCreateOrderRequest.setP3_customerNumber(paySceneDTO.getMerchantId());
		BigDecimal amount = new BigDecimal(tradeOrder.getAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
		appPayWapCreateOrderRequest.setP4_orderAmount(String.valueOf(amount));
		appPayWapCreateOrderRequest.setP5_currency("CNY");
		appPayWapCreateOrderRequest.setP6_orderIp(ip);
		appPayWapCreateOrderRequest.setP7_notifyUrl(payCommonProperties.getHeliPayConfig().getPayNotifyUrl());
		appPayWapCreateOrderRequest.setP8_appPayType(getAppPayType());
		appPayWapCreateOrderRequest.setP9_payType(PayType.WAP.name());
		appPayWapCreateOrderRequest.setP10_appName("旅游商城");
		appPayWapCreateOrderRequest.setP11_deviceInfo("AND_WAP");
		appPayWapCreateOrderRequest.setP12_applicationId(goodsOrder.getGoodsName());
		log.info("[订单支付-请求合利宝-WAP(H5)支付 - 统一收单] 请求appId:{}, params:{}", appId, JSON.toJSONString(appPayWapCreateOrderRequest));
//        Map<String,String> map = null;
//        try {
//            map = HeliBeanUtils.convertBean(appPayAppletOrderRequest,new LinkedHashMap<>());
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException(e);
//        }
//        String sign = HeliSignatureUtils.getSignAndEncryptedByReq(map, AppPayAppletOrderRequest.NEED_SIGN_PARAMS,
//				AppPayAppletOrderRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
//		map.put("sign", sign);
		Map<String, String> map = HeliSignatureUtils.convertRequestAndCreateSign(appPayWapCreateOrderRequest,
				AppPayPublicOrderRequest.NEED_SIGN_PARAMS, AppPayPublicOrderRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
		AppPayWapCreateOrderResponse response = heliAppPayClient.wapCreateOrder(payCommonProperties.getHeliPayConfig().getPayUrl(),map);
		String result = HttpUtils.doExecute(payCommonProperties.getHeliPayConfig().getPayUrl(), map);
		log.info("[订单支付-请求合利宝-WAP(H5)支付 - 统一收单] 返回:{}", result);
		AppPayWapCreateOrderResponse appPayWapCreateOrderResponse = JSONUtil.toBean(result, AppPayWapCreateOrderResponse.class);
		AssertUtil.isTrue(HeliPayConstant.SUCCESS_RETURN_CODE.equals(appPayWapCreateOrderResponse.getRt2_retCode()), appPayWapCreateOrderResponse.getRt3_retMsg());
		return appPayWapCreateOrderResponse.getRt8_payInfo();
	}
}
