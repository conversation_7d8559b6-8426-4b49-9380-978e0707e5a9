package com.eyuan.pay.handler.impl.wx;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eyuan.pay.constant.PayConstants;
import com.eyuan.pay.dto.TradeNoticeDTO;
import com.eyuan.pay.entity.PayChannelConfig;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayChannelEnum;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.enums.TradeNotifyTypeEnum;
import com.eyuan.pay.facade.PayResultNoticeSubSystemFacade;
import com.eyuan.pay.handler.MessageDuplicateCheckerHandler;
import com.eyuan.pay.handler.impl.AbstractPayNotifyCallbackHandler;
import com.eyuan.pay.service.PayChannelConfigService;
import com.eyuan.pay.service.PayGoodsOrderService;
import com.eyuan.pay.service.PayTradeOrderService;
import com.eyuan.pay.utils.OrderStatusEnum;
import com.eyuan.pay.utils.PayUtil;
import com.eyuan.pay.utils.TradeStatusEnum;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.kit.WxPayKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-27
 * <p>
 * 微信回调处理
 */
@Slf4j
@AllArgsConstructor
@Service("weChatCallback")
public class WeChatPayNotifyCallbackHandler extends AbstractPayNotifyCallbackHandler<PayGoodsOrder> {
	private final MessageDuplicateCheckerHandler duplicateCheckerHandler;
	private final PayTradeOrderService tradeOrderService;
	private final PayGoodsOrderService goodsOrderService;
	private final PayResultNoticeSubSystemFacade payResultNoticeSubSystemFacade;
	private final PayChannelConfigService payChannelConfigService;

	@Override
	protected String getOrderNo(Map<String, String> params) {
		return params.get(PayConstants.OUT_TRADE_NO);
	}

	@Override
	protected String getThirdOrderNo(Map<String, String> params) {
		return params.get("transaction_id");
	}

	/**
	 * 去重处理
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean duplicateChecker(Map<String, String> params) {
		// 判断10秒内是否已经回调处理
		if (duplicateCheckerHandler.isDuplicate(getOrderNo(params))) {
			log.info("[微信支付回调] 微信订单重复回调, 不做处理, params:{}", params);
			this.saveNotifyRecord(params, "重复回调");
			return true;
		}
		return false;
	}

	/**
	 * 验签逻辑
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean verifyNotify(Map<String, String> params) {
		log.info("[微信支付回调] 签名校验: params:{}", JSON.toJSONString(params));
		String appid = params.get("appid");
		PayChannelConfig payChannelConfig = payChannelConfigService.getByChannelId(PayChannelEnum.WECHAT_PAY.getValue());
		String verifySignKey = payChannelConfig.getMerchantKey();
		if (verifySignKey == null) {
			log.error("[微信支付回调] 签名校验: 获取商户秘钥信息失败, appId:{}", appid);
			return false;
		}
		if (!WxPayKit.verifyNotify(params, verifySignKey, SignType.HMACSHA256)) {
			log.warn("[微信支付回调] 微信支付回调验签失败");
			return false;
		}
		log.warn("[微信支付回调] 签名成功, params:{}", JSON.toJSONString(params));
		return true;
	}

	/**
	 * 解析报文
	 *
	 * @param params
	 * @return
	 */
	@Override
	public TradeNoticeDTO parse(Map<String, String> params,PayGoodsOrder payGoodsOrder) {
		log.info("[微信支付回调], 参数解析:{}", JSON.toJSONString(params));
		String tradeStatus = EnumUtil.fromString(TradeStatusEnum.class, params.get(PayConstants.RESULT_CODE)).getStatus();
		String orderNo = payGoodsOrder.getPayOrderId();
		PayGoodsOrder updatePayGoodsOrder = new PayGoodsOrder();
		updatePayGoodsOrder.setGoodsOrderId(payGoodsOrder.getGoodsOrderId());
		updatePayGoodsOrder.setStatus(tradeStatus);
		boolean updateFlag = goodsOrderService.updateById(updatePayGoodsOrder);
		log.info("[微信支付回调], 更新商品订单信息 flag:{}", updateFlag);

		PayTradeOrder tradeOrder = tradeOrderService.getOne(Wrappers.<PayTradeOrder>lambdaQuery()
				.eq(PayTradeOrder::getOrderId, orderNo));
		if (Objects.isNull(tradeOrder)) {
			log.info("[微信支付回调], 交易订单信息为空, 订单号:{}", orderNo);
		}
		Date succTime = MapUtil.getDate(params, "time_end");
		tradeOrder.setPaySuccTime(succTime.getTime());
		tradeOrder.setStatus(tradeStatus);
		tradeOrder.setChannelOrderNo(params.get("transaction_id"));
		tradeOrder.setErrMsg(params.get("err_code_des"));
		tradeOrder.setErrCode(params.get("err_code"));
		updateFlag = tradeOrderService.updateById(tradeOrder);

		log.info("[微信支付回调], 更新交易订单信息 flag:{}", updateFlag);

		TradeStatusEnum statusEnum = EnumUtil.fromString(TradeStatusEnum.class, params.get(PayConstants.RESULT_CODE));
		PayToolEnum payToolEnum = PayToolEnum.valueOf(tradeOrder.getChannelId());
		TradeNoticeDTO tradeNoticeDTO = TradeNoticeDTO.builder().amount(BigDecimal.valueOf(Long.parseLong(tradeOrder.getAmount())).divide(BigDecimal.valueOf(100)))
				.tradeNo(orderNo).outTradeNo(tradeOrder.getChannelOrderNo()).tradeStatus(TradeStatusEnum.SUCCESS == statusEnum)
				.tenantId(tradeOrder.getTenantId()).tradeTime(succTime).payChannel(payToolEnum.value()).platformType(payGoodsOrder.getPlatformType()).build();
		return tradeNoticeDTO;
	}


	@Override
	public void noticeSubSys(TradeNoticeDTO params) {
		payResultNoticeSubSystemFacade.noticeSubSystem(params);
	}

	@Override
	public String notifyType() {
		return TradeNotifyTypeEnum.PAY_NOTIFY.value();
	}

	@Override
	protected boolean idempotentChecker(PayGoodsOrder data) {
		return OrderStatusEnum.SUCCESS.getStatus().equals(data.getStatus());
	}

	@Override
	protected String buildResult(Map<String, String> params) {
		Map<String, String> xml = new HashMap<>(4);
		xml.put("return_code", "SUCCESS");
		xml.put("return_msg", "OK");
		return WxPayKit.toXml(xml);
	}

	@Override
	protected PayGoodsOrder getByOrderNoAndInit(String orderNo) {
		PayGoodsOrder goodsOrder = goodsOrderService.getByPayOrderId(orderNo);
		if (Objects.isNull(goodsOrder)) {
			log.info("[微信支付回调], 商品订单信息为空, 订单号:{}", orderNo);
		}
		PayUtil.setContextTenantId(goodsOrder.getTenantId());
		return goodsOrder;
	}
}
