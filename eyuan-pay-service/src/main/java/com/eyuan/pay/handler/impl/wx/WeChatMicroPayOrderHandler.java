package com.eyuan.pay.handler.impl.wx;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.utils.OrderStatusEnum;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.WxPayApiConfig;
import com.ijpay.wxpay.model.MicroPayModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-05-31
 * <p>
 * 付款码支付
 */
@Slf4j
@Service
public class WeChatMicroPayOrderHandler extends AbstractWxPayOrderHandler {
	@Autowired
	private HttpServletRequest request;

	/**
	 * 创建交易订单
	 *
	 * @param goodsOrder
	 * @return
	 */
	@Override
	public PayTradeOrder createTradeOrder(PayGoodsOrder goodsOrder, WxPayApiConfig wxPayApiConfig) {

		PayTradeOrder payTradeOrder = tradeOrderMapper.selectOne(new LambdaQueryWrapper<PayTradeOrder>().eq(PayTradeOrder::getOrderId, goodsOrder.getPayOrderId()));
		if (Objects.nonNull(payTradeOrder)) {
			log.info("[支付] 微信, 重复交易订单, payOrderId:{}", goodsOrder.getPayOrderId());
			return payTradeOrder;
		}
		PayTradeOrder tradeOrder = new PayTradeOrder();
		tradeOrder.setOrderId(goodsOrder.getPayOrderId());
		tradeOrder.setAmount(goodsOrder.getAmount());
		tradeOrder.setChannelId(PayToolEnum.WEIXIN_PAYMENT_CODE.name());
		tradeOrder.setChannelMchId(wxPayApiConfig.getMchId());
		tradeOrder.setClientIp(ServletUtil.getClientIP(request));
		tradeOrder.setCurrency("CNY");
		tradeOrder.setStatus(OrderStatusEnum.INIT.getStatus());
		tradeOrder.setBody(goodsOrder.getGoodsName());
		tradeOrderMapper.insert(tradeOrder);
		return tradeOrder;
	}

	/**
	 * 调起渠道支付
	 *
	 * @param goodsOrder 商品订单
	 * @param tradeOrder 交易订单
	 */
	@Override
	public Object pay(PayGoodsOrder goodsOrder, PayTradeOrder tradeOrder,WxPayApiConfig wxPayApiConfig) {
		String ip = ServletUtil.getClientIP(request);
		String appId = wxPayApiConfig.getAppId();
		String tenantId = getTenantId();
		Map<String, String> params = MicroPayModel.builder().appid(appId).mch_id(wxPayApiConfig.getMchId()).attach(tenantId)
				.body(goodsOrder.getGoodsName()).spbill_create_ip(ip).total_fee(goodsOrder.getAmount()).openid(goodsOrder.getUserId()).
				out_trade_no(tradeOrder.getOrderId()).auth_code(goodsOrder.getPaymentCode()).build()
				.createSign(wxPayApiConfig.getPartnerKey(), SignType.HMACSHA256);
		log.info("[订单支付-请求微信-付款码支付 - 统一收单] 请求appId:{}, params:{}", appId, JSON.toJSONString(params));
		// 发送请求
		String xmlResult = WxPayApi.pushOrder(false,params);
		log.info("[订单支付-请求微信-付款码支付 - 统一收单] 返回:{}", xmlResult);
		// 将请求返回的 xml 数据转为 Map，方便后面逻辑获取数据
		Map<String, String> resultMap = WxPayKit.xmlToMap(xmlResult);
		// 判断返回的结果
		String returnCode = resultMap.get("return_code");
		String returnMsg = resultMap.get("return_msg");
		AssertUtil.isTrue(WxPayKit.codeIsOk(returnCode), returnMsg);

		String prepayId = resultMap.get("prepay_id");
		// 以下字段在return_code 和result_code都为SUCCESS的时候有返回
		// 二次签名，构建公众号唤起支付的参数,这里的签名方式要与上面统一下单请求签名方式保持一致
		Map<String, String> resultParams = WxPayKit.prepayIdCreateSign(prepayId,
				wxPayApiConfig.getAppId(),wxPayApiConfig.getPartnerKey(),SignType.HMACSHA256);
		// 将二次签名构建的数据返回给前端并唤起公众号支付
		log.info("[订单支付-请求微信-付款码支付 - 统一收单] 最终返回:{}", JSON.toJSONString(resultParams));
		return resultParams;
	}


	/**
	 * {"nonce_str":"1649299050373","openid":"ohl-y5Zqm0KmJXDroQyt9Omh86AM","sign":"7CC56B1653BD30699699EA08C82229FE",
	 * "sub_mch_id":"1624052298","mch_id":"1618163059","body":"大床房",
	 * "notify_url":"https://minipro.jbmy.com.cn/pay/rest/notify/wx/callbak","spbill_create_ip":"*************",
	 * "out_trade_no":"hm001511937047781314560","appid":"wxdf1531abcf8e4000","total_fee":"1","trade_type":"JSAPI","attach":"45"}
	 * @param args
	 */
	public static void main(String[] args) {
//		String params = "{\"nonce_str\":\"1649299050373\",\"sub_openid\":\"ohl-y5Zqm0KmJXDroQyt9Omh86AM\",\"sub_mch_id\":\"1624052298\",\"mch_id\":\"1618163059\",\"body\":\"大床房\",\"notify_url\":\"https://minipro.jbmy.com.cn/pay/rest/notify/wx/callbak\",\"spbill_create_ip\":\"*************\",\"out_trade_no\":\"hm001511937047781314560\",\"sub_appid\":\"wxdf1531abcf8e4000\",\"appid\":\"wx506612d0c0640d99\",\"total_fee\":\"1\",\"trade_type\":\"JSAPI\",\"attach\":\"45\"}";
//		Map map = JSON.parseObject(params, Map.class);
//		map.put("sign", PaymentKit.createSign(map, "OvlrIwhvEAHN5W4VN7qDkirdcsNShqwj"));
//		String xmlResult = WxPayApi.pushOrder(false, map);
//		System.out.println(xmlResult);

		Map<String, String> packageParams = new HashMap();
		packageParams.put("appId", "wxdf1531abcf8e4000");
		packageParams.put("timeStamp", String.valueOf(System.currentTimeMillis()));
		packageParams.put("nonceStr", "k1n8zVRc5yfMpTYI");
		packageParams.put("package", "prepay_id=wx07161200594659febad800f1f7e6e90000");
		packageParams.put("signType", "MD5");
		String packageSign = WxPayKit.createSign(packageParams, "OvlrIwhvEAHN5W4VN7qDkirdcsNShqwj");
		packageParams.put("paySign", packageSign);
		System.out.println(JSON.toJSONString(packageParams));
	}

	@Override
	public Integer payChannel() {
		return PayToolEnum.WEIXIN_PAYMENT_CODE.value();
	}
}
