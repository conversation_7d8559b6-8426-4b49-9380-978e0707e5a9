package com.eyuan.pay.handler;

import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayRefundOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.request.OrderRefundRequest;
import com.eyuan.pay.response.OrderRefundResponse;

/**
 * <AUTHOR>
 * @description 退款处理接口
 * @date 2020-08-28 11:29
 */
public interface RefundOrderHandler {
	PayRefundOrder createRefundOrder(OrderRefundRequest request);

	OrderRefundResponse refund(PayRefundOrder payRefundOrder, PaySceneDTO paySceneConfig);

	void doAfter(PayRefundOrder payRefundOrder);

	OrderRefundResponse handle(OrderRefundRequest request);

	/**
	 * 支付渠道
	 * @see com.eyuan.pay.enums.PayChannelEnum
	 * @return
	 */
	String payChannel();

}
