package com.eyuan.pay.handler.impl.wx;


import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.handler.impl.AbstractPayOrderHandler;

import com.eyuan.pay.utils.PayApiConfigBuildUtil;
import com.eyuan.pay.utils.PayUtil;
import com.ijpay.wxpay.WxPayApiConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * 微信支付抽象处理
 * <AUTHOR>
 * @date 2019-05-31
 */
@Slf4j
public abstract class AbstractWxPayOrderHandler extends AbstractPayOrderHandler<WxPayApiConfig> {

	@Override
	protected WxPayApiConfig convertConfig(PaySceneDTO paySceneDTO) {
		return PayApiConfigBuildUtil.buildWxPayApiConfig(paySceneDTO);
	}
	protected String getTenantId() {
		return PayUtil.getContextTenantId();
	}
}
