package com.eyuan.pay.handler.impl.wx;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.constant.PayConstants;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayRefundOrder;
import com.eyuan.pay.enums.*;
import com.eyuan.pay.handler.impl.AbstractRefundOrderHandler;
import com.eyuan.pay.response.OrderRefundResponse;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.WxPayApiConfig;
import com.ijpay.wxpay.model.RefundModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 微信退款
 * @date 2020-08-28 11:46
 */
@Slf4j
@Service
public class WeChatRefundOrderHandler extends AbstractRefundOrderHandler {
	@Autowired
	private PayCommonProperties payCommonProperties;

	@Override
	public OrderRefundResponse refund(PayRefundOrder payRefundOrder, PaySceneDTO paySceneConfig) {
		RefundModel refundModel = RefundModel.builder().appid(paySceneConfig.getAppId()).mch_id(paySceneConfig.getMerchantId())
				.nonce_str(WxPayKit.generateStr()).transaction_id(payRefundOrder.getChannelOrderNo()).total_fee(payRefundOrder.getPayAmount())
				.refund_fee(String.valueOf(payRefundOrder.getRefundAmount())).out_refund_no(payRefundOrder.getMchRefundNo())
				.notify_url(payCommonProperties.getWxPayConfig().getRefundNotifyUrl()).build();
		if (PayModeEnum.SERVICE.getValue().equals(paySceneConfig.getMode())) {
			refundModel.setSub_mch_id(paySceneConfig.getSubMerchantId());
		}
		Map<String, String> params = refundModel.createSign(paySceneConfig.getMerchantKey(), SignType.HMACSHA256);
		String certPath = PayConstants.CERT_PATH.concat(paySceneConfig.getMerchantId()).concat("/").concat(PayConstants.CERT_FILE_NAME);
		String certPass = paySceneConfig.getMerchantId();
		log.info("[订单退款-请求微信 - 申请退款] 请求appId:{}, mchId:{}, paternerKey:{}, params:{}, certPath is:{}, certKey:{}",
				paySceneConfig.getAppId(), paySceneConfig.getMerchantId(), paySceneConfig.getPrivateKey(), JSON.toJSONString(params), certPath, certPass);
		String xmlResult = WxPayApi.orderRefund(false, params, certPath, certPass);
		log.info("[订单退款-请求微信 - 申请退款] 返回:{}", xmlResult);
		Map<String, String> resultMap = WxPayKit.xmlToMap(xmlResult);
		String returnCode = resultMap.get("return_code");
		AssertUtil.isTrue(WxPayKit.codeIsOk(returnCode), "微信退款接口返回失败," + resultMap.get("return_msg"));
		String resultCode = resultMap.get("result_code");
		AssertUtil.isTrue(WxPayKit.codeIsOk(resultCode), "微信退款返回业务失败," + resultMap.get("err_code_des"));
		return OrderRefundResponse.builder().payOrderId(payRefundOrder.getPayOrderId()).mchRefundNo(payRefundOrder.getMchRefundNo())
				.outRefundNo(resultMap.get("refund_id")).build();
	}

	@Override
	public String payChannel() {
		return PayChannelEnum.WECHAT_PAY.value();
	}

	/**
	 * appId:wx506612d0c0640d99, mchId:1618163059, paternerKey:OvlrIwhvEAHN5W4VN7qDkirdcsNShqwj,
	 * params:{"transaction_id":"4200001364202204070570976744","nonce_str":"4zycsqjb2xmgh4c0uh7d7hxvgikmrld7",
	 * "out_refund_no":"hr001512271997743271936","appid":"wx506612d0c0640d99","total_fee":"1","refund_fee":"1",
	 * "sign":"8DBBB9E4B1C30945143F11D3CC58A9BC","mch_id":"1618163059"}, certPath is:/root/cert/45/apiclient_cert.p12, certKey:1618163059
	 * @param args
	 */
	public static void main(String[] args) {
//		String mchId = "1618163059";
////		WxPayApiConfig wxPayApiConfig = WxPayApiConfig.New().setAppId("wx506612d0c0640d99")
////				.setMchId(mchId).setPaternerKey("12345678901234567890123456789012").setSubMchId("1651107671");
//		WxPayApiConfig wxPayApiConfig = WxPayApiConfig.builder().slAppId("wx506612d0c0640d99")
//				.slMchId(mchId).partnerKey("12345678901234567890123456789012").mchId("1651107671").build();
//		Map<String, String> params = new HashMap<>();
//		params.put("appid", wxPayApiConfig.getSlAppId());
//		params.put("mch_id", wxPayApiConfig.getSlMchId());
//		params.put("sub_mch_id", wxPayApiConfig.getMchId());
//		params.put("nonce_str", RandomUtil.randomString(32));
//		params.put("transaction_id", "4200002698202506024404299271");
//		params.put("total_fee", "4000");
//		params.put("refund_fee", "4000");
//		params.put("out_refund_no", "115596186551728");
//		params.put("sign", WxPayKit.createSign(params, wxPayApiConfig.getPartnerKey()));
//		/*InputStream stream = null;
//		try {
//			stream = ResourceUtil.getStream("classpath:config/apiclient_cert.p12");
//		} catch (Exception e) {
//			log.error("[订单退款] 获取微信证书失败, e:", e);
//		}*/
//		/*String certPath = payCommonProperties.getWxPayConfig().getCertPath();
//		String certPass = payCommonProperties.getWxPayConfig().getCertKey();*/
//		String certPath = "/Users/<USER>/cert/"+mchId+"/apiclient_cert.p12";
//
//		String certPass = wxPayApiConfig.getSlMchId();
//		log.info("[订单退款-请求微信 - 申请退款] 请求appId:{}, mchId:{}, paternerKey:{}, params:{}, certPath is:{}, certKey:{}",
//				wxPayApiConfig.getAppId(), wxPayApiConfig.getMchId(), wxPayApiConfig.getPartnerKey(), JSON.toJSONString(params), certPath, certPass);
//		String xmlResult = WxPayApi.orderRefund(false, params, certPath, certPass);
//		log.info("[订单退款-请求微信 - 申请退款] 返回:{}", xmlResult);
//		Map<String, String> resultMap = WxPayKit.xmlToMap(xmlResult);


		String merchantId = "1618163059";
		String subMerchantId = "1651107671";
		String appId = "wx506612d0c0640d99";
		String refundNo = "4200002698202506025515299832";
		String refundAmount = "36000";
		String payAmount = "36000";
		String partnerKey = "12345678901234567890123456789012";
		String channelOrderNo = "4200002713202506149675644345";
		RefundModel refundModel = RefundModel.builder().appid(appId).mch_id(merchantId)
				.nonce_str(WxPayKit.generateStr()).transaction_id(channelOrderNo).total_fee(payAmount)
				.refund_fee(refundAmount).out_refund_no(refundNo).sub_mch_id(subMerchantId)
				.build();
		Map<String, String> params = refundModel.createSign(partnerKey, SignType.HMACSHA256);
		String certPath = "/Users/<USER>/cert/".concat(merchantId).concat("/").concat(PayConstants.CERT_FILE_NAME);
        String xmlResult = WxPayApi.orderRefund(false, params, certPath, merchantId);
		log.info("[订单退款-请求微信 - 申请退款] 返回:{}", xmlResult);

	}

}
