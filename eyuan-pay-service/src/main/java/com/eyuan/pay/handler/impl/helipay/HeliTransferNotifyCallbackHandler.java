package com.eyuan.pay.handler.impl.helipay;

import com.alibaba.fastjson.JSON;
import com.eyuan.pay.dto.TradeNoticeDTO;
import com.eyuan.pay.entity.PayTradeRecord;
import com.eyuan.pay.enums.PayTradeStatusEnum;
import com.eyuan.pay.enums.SettlementTradeTypeEnum;
import com.eyuan.pay.enums.TradeNotifyTypeEnum;
import com.eyuan.pay.handler.impl.AbstractPayNotifyCallbackHandler;
import com.eyuan.pay.helipay.constant.HeliPayConstant;
import com.eyuan.pay.handler.MessageDuplicateCheckerHandler;
import com.eyuan.pay.helipay.enums.TransferOrderStatus;
import com.eyuan.pay.helipay.response.HeliTransferNotifyResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.service.PayTradeRecordService;
import com.eyuan.pay.settlement.facade.SettlementFacade;
import com.eyuan.pay.settlement.facade.WithdrawFacade;
import com.eyuan.pay.utils.PayUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-27
 * <p>
 * 合利宝回调处理
 */
@Slf4j
@AllArgsConstructor
@Service("heliTransferCallback")
public class HeliTransferNotifyCallbackHandler extends AbstractPayNotifyCallbackHandler<PayTradeRecord> {
	private final MessageDuplicateCheckerHandler duplicateCheckerHandler;
	private final PayTradeRecordService payTradeRecordService;
	private final SettlementFacade settlementFacade;
	private final WithdrawFacade withdrawFacade;


	/**
	 * 去重处理
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean duplicateChecker(Map<String, String> params) {
		String orderNo = getOrderNo(params);
		// 判断10秒内是否已经回调处理
		if (duplicateCheckerHandler.isDuplicate(orderNo)) {
			log.info("[合利宝代付回调] 重复回调, 不做处理, params:{}", params);
			this.saveNotifyRecord(params, "重复回调");
			return true;
		}
		return false;
	}

	/**
	 * 验签逻辑
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean verifyNotify(Map<String, String> params) {
		boolean verify = HeliSignatureUtils.verifySignByRes(params, HeliTransferNotifyResponse.NEED_SIGN_PARAMS);
		if (!verify) {
			log.warn("[合利宝代付回调] 回调验签失败");
			return false;
		} else {
			return true;
		}
	}

	@Override
	protected boolean idempotentChecker(PayTradeRecord payTradeRecord) {
		return PayTradeStatusEnum.SUCCESS.getValue().equals(payTradeRecord.getStatus());
	}

	@Override
	protected String buildResult(Map<String, String> params) {
		return "success";
	}

	/**
	 * 解析报文
	 *
	 * @param params
	 * @return
	 */
	@Override
	public TradeNoticeDTO parse(Map<String, String> params,PayTradeRecord payTradeRecord) {
		log.info("[合利宝代付回调], 参数解析:{}", JSON.toJSONString(params));
		String resultCode = params.get("rt2_retCode");
		String orderStatus = params.get("rt7_orderStatus");
		String thirdOrderId = params.get("rt6_serialNumber");
		Boolean tradeStatus = false;
		if(HeliPayConstant.SUCCESS_RETURN_CODE.equals(resultCode)){
			PayTradeRecord updatePayTradeRecord = new PayTradeRecord();
			updatePayTradeRecord.setId(payTradeRecord.getId());
			updatePayTradeRecord.setStatus(PayTradeStatusEnum.SUCCESS.getValue());
			payTradeRecordService.updateById(updatePayTradeRecord);
			if(TransferOrderStatus.SUCCESS.name().equals(orderStatus)){
				tradeStatus = true;
			}
		} else {
			PayTradeRecord updatePayTradeRecord = new PayTradeRecord();
			updatePayTradeRecord.setId(payTradeRecord.getId());
			updatePayTradeRecord.setStatus(PayTradeStatusEnum.FAIL.getValue());
			payTradeRecordService.updateById(updatePayTradeRecord);
		}
		TradeNoticeDTO tradeNoticeDTO = TradeNoticeDTO.builder().tradeStatus(tradeStatus).tenantId(payTradeRecord.getTenantId())
				.settlementTradeType(payTradeRecord.getTradeType())
				.tradeNo(payTradeRecord.getOrderNo()).outTradeNo(thirdOrderId).failReason(params.get("rt3_retMsg")).build();
		return tradeNoticeDTO;
	}

	@Override
	protected String getThirdOrderNo(Map<String, String> params) {
		return params.get("rt6_serialNumber");
	}


	@Override
	public void noticeSubSys(TradeNoticeDTO tradeNoticeDTO) {
		if (SettlementTradeTypeEnum.SETTLEMENT.getValue().equals(tradeNoticeDTO.getSettlementTradeType())) {
			settlementFacade.updateSettlementStatusAndNotify(tradeNoticeDTO.getTradeNo(),tradeNoticeDTO.getTradeStatus(),tradeNoticeDTO.getFailReason());
		} else if(SettlementTradeTypeEnum.WITHDRAW.getValue().equals(tradeNoticeDTO.getSettlementTradeType())) {
			withdrawFacade.updateSettlementStatusAndNotify(tradeNoticeDTO.getTradeNo(),tradeNoticeDTO.getTradeStatus(),tradeNoticeDTO.getFailReason());
		}
	}

	@Override
	public String notifyType() {
		return TradeNotifyTypeEnum.TRANSFER_NOTIFY.value();
	}

	@Override
	protected PayTradeRecord getByOrderNoAndInit(String orderNo) {
		PayTradeRecord payTradeRecord = payTradeRecordService.getByTradeNo(orderNo);
		if (Objects.isNull(payTradeRecord)) {
			log.info("[合利宝代付回调], 代付交易信息为空, 订单号:{}", orderNo);
		}
		PayUtil.setContextTenantId(payTradeRecord.getTenantId());
		return payTradeRecord;
	}

	@Override
	protected String getOrderNo(Map<String, String> params) {
		return params.get("rt5_orderId");
	}
}
