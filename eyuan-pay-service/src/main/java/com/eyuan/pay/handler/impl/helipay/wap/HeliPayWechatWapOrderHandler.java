package com.eyuan.pay.handler.impl.helipay.wap;

import com.eyuan.pay.enums.PayToolEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * <p>
 * 合利宝微信WAP(H5)支付
 */
@Service
public class HeliPayWechatWapOrderHandler extends AbstractHeliPayWapOrderHandler {
	@Override
	public Integer payChannel() {
		return PayToolEnum.HELIPAY_WEIXIN_WAP.value();
	}

	@Override
	protected PayToolEnum getPayToolEnum() {
		return PayToolEnum.HELIPAY_WEIXIN_WAP;
	}
}
