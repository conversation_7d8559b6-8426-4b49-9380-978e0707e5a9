package com.eyuan.pay.handler.impl;

import cn.hutool.core.lang.Assert;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayRefundOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.handler.RefundOrderHandler;
import com.eyuan.pay.request.OrderRefundRequest;
import com.eyuan.pay.response.OrderRefundResponse;
import com.eyuan.pay.service.PayChannelConfigService;
import com.eyuan.pay.service.PayRefundOrderService;
import com.eyuan.pay.service.PayTradeOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description 退款处理基础逻辑
 * @date 2020-08-28 11:33
 */
@Slf4j
public abstract class AbstractRefundOrderHandler implements RefundOrderHandler {

	@Autowired
	private PayRefundOrderService payRefundOrderService;
	@Autowired
	private PayTradeOrderService payTradeOrderService;
	@Autowired
	private PayChannelConfigService payChannelConfigService;

	@Override
	public PayRefundOrder createRefundOrder(OrderRefundRequest request) {
		log.info("[订单退款] 请求参数:{}", request);
		Assert.notBlank(request.getPayOrderId(), "参数错误，支付订单号号不能为空");
		Assert.notBlank(request.getMchRefundNo(), "参数错误，商户退款单号不能为空");
		Assert.notNull(request.getOrderPayAmt(), "参数错误，订单支付金额不能为空");
		Assert.notNull(request.getRefundAmt(), "参数错误，退款金额不能为空");
		Assert.notBlank(request.getGoodsName(), "参数错误，商品名称不能为空");
		Assert.notBlank(request.getPayChannelId(), "参数错误，支付渠道不能为空");

		PayTradeOrder payTradeOrder = payTradeOrderService.getById(request.getPayOrderId());
		Assert.notNull(payTradeOrder, "支付订单信息不存在");

		PayRefundOrder payRefundOrder = new PayRefundOrder();

		payRefundOrder.setPayAmount(String.valueOf(request.getOrderPayAmt()));
		payRefundOrder.setRefundAmount(request.getRefundAmt());
		payRefundOrder.setPayOrderId(request.getPayOrderId());
		// 三方支付订单号
		payRefundOrder.setChannelPayOrderNo(payTradeOrder.getChannelOrderNo());
		payRefundOrder.setChannelOrderNo(payTradeOrder.getChannelOrderNo());
		payRefundOrder.setMchRefundNo(request.getMchRefundNo());
		payRefundOrder.setRemark(request.getGoodsName());
		payRefundOrder.setStatus("0");
		payRefundOrderService.save(payRefundOrder);
		log.info("[订单退款] 保存退款记录成功, 支付渠道订单号:{}", payRefundOrder.getChannelOrderNo());
		return payRefundOrder;
	}

	@Override
	public void doAfter(PayRefundOrder payRefundOrder) {
		payRefundOrderService.updateById(payRefundOrder);
	}

	@Override
	public OrderRefundResponse handle(OrderRefundRequest request) {
		PayRefundOrder refundOrder = createRefundOrder(request);
		PaySceneDTO paySceneDTO = getPaySceneConfig(request.getPayChannelId());
		OrderRefundResponse result = refund(refundOrder,paySceneDTO);
		doAfter(refundOrder);
		return result;
	}

	private PaySceneDTO getPaySceneConfig(String payChannelId) {
		PayToolEnum payToolEnum = PayToolEnum.valueOf(payChannelId);
		return payChannelConfigService.getPaySceneConfig(payToolEnum.payChanelValue(), payToolEnum.payTypeValue(), payToolEnum.paySceneValue());
	}

}
