package com.eyuan.pay.handler.impl.alipay;

import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.handler.impl.AbstractPayOrderHandler;
import com.eyuan.pay.utils.PayApiConfigBuildUtil;
import com.ijpay.alipay.AliPayApiConfig;

/**
 * 支付宝支付抽象处理
 * @Author: sunwh
 * @Date: 2025/5/15 11:05
 */
public abstract class AbstractAliPayOrderHandler extends AbstractPayOrderHandler<AliPayApiConfig> {

    @Override
    protected AliPayApiConfig convertConfig(PaySceneDTO paySceneDTO) {
        return PayApiConfigBuildUtil.buildAliPayApiConfig(paySceneDTO);
    }
}
