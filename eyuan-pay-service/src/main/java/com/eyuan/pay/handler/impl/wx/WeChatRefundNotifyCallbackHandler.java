package com.eyuan.pay.handler.impl.wx;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.eyuan.pay.dto.TradeNoticeDTO;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayRefundOrder;
import com.eyuan.pay.enums.PayChannelEnum;
import com.eyuan.pay.enums.PayRefundStatusEnum;
import com.eyuan.pay.enums.TradeNotifyTypeEnum;
import com.eyuan.pay.facade.PayResultNoticeSubSystemFacade;
import com.eyuan.pay.handler.MessageDuplicateCheckerHandler;
import com.eyuan.pay.handler.impl.AbstractPayNotifyCallbackHandler;
import com.eyuan.pay.service.PayChannelConfigService;
import com.eyuan.pay.service.PayGoodsOrderService;
import com.eyuan.pay.service.PayRefundOrderService;
import com.eyuan.pay.utils.PayUtil;
import com.eyuan.pay.utils.TradeStatusEnum;
import com.ijpay.core.kit.WxPayKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-27
 * <p>
 * 微信回调处理
 */
@Slf4j
@AllArgsConstructor
@Service("weChatRefundCallback")
public class WeChatRefundNotifyCallbackHandler extends AbstractPayNotifyCallbackHandler<PayRefundOrder> {
	private final MessageDuplicateCheckerHandler duplicateCheckerHandler;
	private final PayRefundOrderService payRefundOrderService;
	private final PayChannelConfigService payChannelConfigService;
	private final PayResultNoticeSubSystemFacade payResultNoticeSubSystemFacade;
	private final PayGoodsOrderService payGoodsOrderService;


	@Override
	@Transactional(rollbackFor = Exception.class)
	public String handle(Map<String, String> params) {
		// 验签处理
		if (!verifyNotify(params)) {
			return null;
		}

		// 去重处理
		if (duplicateChecker(params)) {
			return null;
		}

		//获取商户订单号
		String orderNo = getOrderNo(params);


		//查询订单并且初始化租户
		PayRefundOrder payRefundOrder = getByOrderNoAndInit(orderNo);

		//幂等处理
		if (idempotentChecker(payRefundOrder)) {
			return buildResult(params);
		}

		TradeNoticeDTO tradeNoticeDTO = parse(params,payRefundOrder);
		//组装响应参数
		String result = buildResult(params);
		// 保存处理结果
		saveNotifyRecord(params, result);

		noticeSubSys(tradeNoticeDTO);

		return result;
	}

	/**
	 * 去重处理
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean duplicateChecker(Map<String, String> params) {
		String orderNo = getOrderNo(params);
		// 判断10秒内是否已经回调处理
		if (duplicateCheckerHandler.isDuplicate(orderNo)) {
			log.info("[微信退款回调] 重复回调, 不做处理, params:{}", params);
			this.saveNotifyRecord(params, "重复回调");
			return true;
		}
		return false;
	}

	/**
	 * 验签逻辑
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean verifyNotify(Map<String, String> params) {
		String returnCode = params.get("return_code");
		if (!WxPayKit.codeIsOk(returnCode)) {
			log.warn("[微信退款回调] 微信退款回调失败, params:{}", JSON.toJSONString(params));
			return false;
		}
		log.info("[微信退款回调] 签名校验: params:{}", JSON.toJSONString(params));
		String merchantId = params.get("mch_id");
		String merchantKey = payChannelConfigService.getMerchantKey(PayChannelEnum.WECHAT_PAY.getValue(), merchantId);
		if (merchantKey == null) {
			return false;
		}
		String reqInfoBase64 = params.get("req_info");
		log.info("key:{},reqInfo:{}", merchantKey,reqInfoBase64);
		String decryptedXml = WxPayKit.decryptData(reqInfoBase64, merchantKey);
		params.putAll(WxPayKit.xmlToMap(decryptedXml));
		return true;
	}

	public static void main(String[] args) {
		String reqInfoBase64 = "/dWvHr2nruCe7TYqEsm4Y+CqxAmhJRNR0ZPQ5Nbqz3b7cgI/QwxnjkbjYth1448cC3cZDc1LlzxZw1yW9sRNU1j4rNZfrpB3vCIErVW5BucbAtGQofvGna530zpQWR/+eqfIfzoIS00KlG4JWGQett/iA0/xxpgRUYz/1UbkZL3u+zxcgFIZbxx8AkrNERd8/E6Oxe7Y9fH20yuboOWaMHd7iRQXhNE7Z1HewbqBZip/pxYarRhKnSI7wUZRbTy5n2GELU0KKv++PzzV/jXgnr8RB7JjaAoL8ZdBKfJf52uJ0Nei3jwviR8QZ+eqzz+7h6202auSwwK+SPBLRrtn87a39IN7288uvg4L+1b4Hl8TvE0sg6VtVeVGy8a2fg5RkyV+XrvDqPyh34wvRE3UNYACSZ7kRTYeRCRWRuhQbowtOuDdA+/TWfKPzMsTJMCAWb9jsUd+a+APn4sE3RAsBY7trYYDrl4dkUKI+o10ePDF4giGM+7O/N8i1tXB2nyJd2s59YLqvdg0Dn1cKC7SSzsdiwlKFrYZvhbZzHVWJmLTwDlbWqr9HUfp+5yI15KuJPi/dSko97++a6fBkq4RcfftOI2BrpZBMnDMf5dSaFY7JbKPW4M7gZilLXy+PZEo3OsROIG5/DJ7Dw55ISWJGiaf5dGbwsMCrUGXRirFTUnYZevPZO5IsMPwVpAuBM50glmCtHna9HRmFnhqjDCrOHDhmS8ozc9lBtsABCLKI0O0pVhuTsMCmOBHQthpztTNSxIWTgV75I8fJ/qtxb2HJvpb4CRuMr++cenm9C723QG8M6lkkIQ7Nhc55WRyaPnq16T+vCeopLsQlWbTzaktz1mPawSGpsj4hDtiLvynBVv6W+AkbjK/vnHp5vQu9t0BZy4FvVUqu+9CtB00S7ny8BadCdue8bHJmJ/2gbIXXbGK0LVq9oy1j+ytjSU3afJ46un3gVJ7M9NcfJ2P1HgW6mV2nUibPVsJAK45buiMCF6zEkN0aJ0vGN6mHv1fJVNm7nxhBg3d+ZDW3zZWOfJGTK7Sk5i14a3HJ4a30YcOx+3Qy9Cv113VE6quOpd3qSkJ8SK7XWXpJF1+GNaYE76PiCBKpX6PHMk4YqxbjfF2faKR5ViGo6UbJkCOt9UJLkOGKjCeSgky/8D+WShGZrtToA==";
		String decryptedXml = WxPayKit.decryptData(reqInfoBase64, "12345678901234567890123456789012");		// WxPayApiConfigKit.getWxPayApiConfig()
		System.out.println(decryptedXml);
	}

	/**
	 * 解析报文
	 *
	 * @param params
	 * @return
	 */
	@Override
	public TradeNoticeDTO parse(Map<String, String> params, PayRefundOrder payRefundOrder) {
		log.info("[微信退款回调], 参数解析:{}", JSON.toJSONString(params));
		String refundStatus = params.get("refund_status");
		Date successTime = MapUtil.getDate(params, "success_time");
		if(TradeStatusEnum.SUCCESS.getDescription().equals(refundStatus)){
			PayRefundOrder updatePayRefundOrder = new PayRefundOrder();
			updatePayRefundOrder.setRefundOrderId(payRefundOrder.getRefundOrderId());
			updatePayRefundOrder.setStatus(PayRefundStatusEnum.REFUND_SUCCESS.getValue());
			updatePayRefundOrder.setRefundSuccTime(DateUtil.toLocalDateTime(successTime));
			payRefundOrderService.updateById(updatePayRefundOrder);
		} else {
			PayRefundOrder updatePayRefundOrder = new PayRefundOrder();
			updatePayRefundOrder.setRefundOrderId(payRefundOrder.getRefundOrderId());
			updatePayRefundOrder.setStatus(PayRefundStatusEnum.REFUND_FAILURE.getValue());
			payRefundOrderService.updateById(updatePayRefundOrder);
		}
		PayGoodsOrder payGoodsOrder = payGoodsOrderService.getByPayOrderId(payRefundOrder.getPayOrderId());
		TradeNoticeDTO tradeNoticeDTO = TradeNoticeDTO.builder().amount(BigDecimal.valueOf(payRefundOrder.getRefundAmount()).divide(BigDecimal.valueOf(100)))
				.tradeNo(getOrderNo(params)).outTradeNo(getThirdOrderNo(params)).tradeStatus(Objects.equals(TradeStatusEnum.SUCCESS.getDescription(), refundStatus))
				.tenantId(payGoodsOrder.getTenantId()).tradeTime(successTime).platformType(payGoodsOrder.getPlatformType()).build();
		return tradeNoticeDTO;
	}

	@Override
	protected boolean idempotentChecker(PayRefundOrder data) {
		return PayRefundStatusEnum.REFUND_SUCCESS.getValue().equals(data.getStatus());
	}

	@Override
	protected String buildResult(Map<String, String> params) {
		Map<String, String> xml = new HashMap<>(4);
		xml.put("return_code", "SUCCESS");
		xml.put("return_msg", "OK");
		return WxPayKit.toXml(xml);
	}


	@Override
	protected String getThirdOrderNo(Map<String, String> params) {
		return params.get("refund_id");
	}

	@Override
	public void noticeSubSys(TradeNoticeDTO params) {
		payResultNoticeSubSystemFacade.refundNoticeSubSystem(params);
	}

	@Override
	public String notifyType() {
		return TradeNotifyTypeEnum.REFUND_NOTIFY.value();
	}

	@Override
	protected PayRefundOrder getByOrderNoAndInit(String refundNo) {
		PayRefundOrder payRefundOrder = payRefundOrderService.getByRefundNo(refundNo);
		if (Objects.isNull(payRefundOrder)) {
			log.info("[微信退款回调], 退款信息为空, 订单号:{}", refundNo);
		}
		PayUtil.setContextTenantId(payRefundOrder.getTenantId());
		return payRefundOrder;
	}

	@Override
	protected String getOrderNo(Map<String, String> params) {
		return params.get("out_refund_no");
	}
}
