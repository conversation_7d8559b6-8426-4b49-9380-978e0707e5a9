package com.eyuan.pay.handler.impl.helipay;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eyuan.pay.dto.TradeNoticeDTO;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.enums.TradeNotifyTypeEnum;
import com.eyuan.pay.facade.PayResultNoticeSubSystemFacade;
import com.eyuan.pay.handler.MessageDuplicateCheckerHandler;
import com.eyuan.pay.handler.impl.AbstractPayNotifyCallbackHandler;
import com.eyuan.pay.helipay.enums.HeliPayTradeStatusEnum;
import com.eyuan.pay.helipay.response.NotifyResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.service.PayGoodsOrderService;
import com.eyuan.pay.service.PayTradeOrderService;
import com.eyuan.pay.utils.OrderStatusEnum;
import com.eyuan.pay.utils.PayUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-27
 * <p>
 * 合利宝回调处理
 */
@Slf4j
@AllArgsConstructor
@Service("heliPayCallback")
public class HeliPayNotifyCallbackHandler extends AbstractPayNotifyCallbackHandler<PayGoodsOrder> {
	private final MessageDuplicateCheckerHandler duplicateCheckerHandler;
	private final PayTradeOrderService tradeOrderService;
	private final PayGoodsOrderService goodsOrderService;
	private final PayResultNoticeSubSystemFacade payResultNoticeSubSystemFacade;

	@Override
	protected PayGoodsOrder getByOrderNoAndInit(String orderNo) {
		PayGoodsOrder goodsOrder = goodsOrderService.getByPayOrderId(orderNo);
		if (Objects.isNull(goodsOrder)) {
			log.info("[合利宝支付回调], 商品订单信息为空, 订单号:{}", orderNo);
		}
		PayUtil.setContextTenantId(goodsOrder.getTenantId());
		return goodsOrder;
	}

	@Override
	protected String getOrderNo(Map<String, String> params) {
		return params.get("rt2_orderId");
	}

	/**
	 * 去重处理
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean duplicateChecker(Map<String, String> params) {
		// 判断10秒内是否已经回调处理
		if (duplicateCheckerHandler.isDuplicate(getOrderNo(params))) {
			log.info("[合利宝支付回调] 微信订单重复回调, 不做处理, params:{}", params);
			this.saveNotifyRecord(params, "重复回调");
			return true;
		}
		return false;
	}

	/**
	 * 验签逻辑
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean verifyNotify(Map<String, String> params) {
		boolean verify = HeliSignatureUtils.verifySignByRes(params, NotifyResponse.NEED_SIGN_PARAMS);
		if (!verify) {
			log.warn("[合利宝支付回调] 微信支付回调验签失败");
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 解析报文
	 *
	 * @param params
	 * @return
	 */
	@Override
	public TradeNoticeDTO parse(Map<String, String> params,PayGoodsOrder payGoodsOrder) {
		log.info("[合利宝支付回调], 参数解析:{}", JSON.toJSONString(params));
		String tradeStatus = EnumUtil.fromString(HeliPayTradeStatusEnum.class, params.get("rt4_status")).getStatus();
		String orderNo = payGoodsOrder.getPayOrderId();
		PayGoodsOrder updatePayGoodsOrder = new PayGoodsOrder();
		updatePayGoodsOrder.setGoodsOrderId(payGoodsOrder.getGoodsOrderId());
		updatePayGoodsOrder.setStatus(tradeStatus);
		boolean updateFlag = goodsOrderService.updateById(updatePayGoodsOrder);
		log.info("[合利宝支付回调], 更新商品订单信息 flag:{}", updateFlag);

		PayTradeOrder tradeOrder = tradeOrderService.getOne(Wrappers.<PayTradeOrder>lambdaQuery()
				.eq(PayTradeOrder::getOrderId, orderNo));
		if (Objects.isNull(tradeOrder)) {
			log.info("[合利宝支付回调], 交易订单信息为空, 订单号:{}", orderNo);
		}
		Date successTime = MapUtil.getDate(params, "rt7_timestamp");
		tradeOrder.setPaySuccTime(successTime.getTime());
		tradeOrder.setStatus(tradeStatus);
		tradeOrder.setChannelOrderNo(params.get("rt3_systemSerial"));
		tradeOrderService.updateById(tradeOrder);

		HeliPayTradeStatusEnum statusEnum = EnumUtil.fromString(HeliPayTradeStatusEnum.class, params.get("rt4_status"));
		PayToolEnum payToolEnum = PayToolEnum.valueOf(tradeOrder.getChannelId());
		TradeNoticeDTO tradeNoticeDTO = TradeNoticeDTO.builder().amount(BigDecimal.valueOf(Long.parseLong(tradeOrder.getAmount())).divide(BigDecimal.valueOf(100)))
				.tradeNo(orderNo).outTradeNo(tradeOrder.getChannelOrderNo()).tradeStatus(HeliPayTradeStatusEnum.SUCCESS == statusEnum).tradeTime(successTime)
				.tenantId(tradeOrder.getTenantId()).payChannel(payToolEnum.value()).platformType(payGoodsOrder.getPlatformType()).build();
		return tradeNoticeDTO;
	}


	@Override
	protected boolean idempotentChecker(PayGoodsOrder data) {
		return OrderStatusEnum.SUCCESS.getStatus().equals(data.getStatus());
	}

	@Override
	protected String buildResult(Map<String, String> params) {
		return "success";
	}

	@Override
	protected String getThirdOrderNo(Map<String, String> params) {
		return params.get("rt3_systemSerial");
	}

	@Override
	public void noticeSubSys(TradeNoticeDTO tradeNoticeDTO) {
		payResultNoticeSubSystemFacade.noticeSubSystem(tradeNoticeDTO);
	}

	@Override
	public String notifyType() {
		return TradeNotifyTypeEnum.PAY_NOTIFY.value();
	}
}
