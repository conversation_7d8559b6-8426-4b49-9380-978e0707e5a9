package com.eyuan.pay.handler.impl.alipay;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.utils.OrderStatusEnum;
import com.eyuan.pay.utils.PayUtil;
import com.ijpay.alipay.AliPayApi;
import com.ijpay.alipay.AliPayApiConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2019-05-31
 * <p>
 * 支付宝手机APP支付
 */
@Slf4j
@Service
@AllArgsConstructor
public class AlipayAppPayOrderHandler extends AbstractAliPayOrderHandler {
	private final PayCommonProperties payCommonProperties;
	private final HttpServletRequest request;

	/**
	 * 创建交易订单
	 *
	 * @param goodsOrder
	 * @return
	 */
	@Override
	public PayTradeOrder createTradeOrder(PayGoodsOrder goodsOrder, AliPayApiConfig aliPayApiConfig) {
		PayTradeOrder tradeOrder = new PayTradeOrder();
		tradeOrder.setOrderId(goodsOrder.getPayOrderId());
		tradeOrder.setAmount(goodsOrder.getAmount());
		tradeOrder.setChannelId(PayToolEnum.ALIPAY_WAP.name());
		tradeOrder.setChannelMchId(aliPayApiConfig.getAppId());
		tradeOrder.setClientIp(ServletUtil.getClientIP(request));
		tradeOrder.setCurrency("cny");
		tradeOrder.setExpireTime(Long.parseLong(payCommonProperties.getAliPayConfig().getExpireTime()));
		tradeOrder.setStatus(OrderStatusEnum.INIT.getStatus());
		tradeOrder.setBody(goodsOrder.getGoodsName());
		tradeOrderMapper.insert(tradeOrder);
		return tradeOrder;
	}

	/**
	 * 调起渠道支付
	 *
	 * @param goodsOrder 商品订单
	 * @param tradeOrder 交易订单
	 */
	@Override
	public AlipayTradeAppPayResponse pay(PayGoodsOrder goodsOrder, PayTradeOrder tradeOrder, AliPayApiConfig aliPayApiConfig) {
		AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
		model.setBody(tradeOrder.getBody());
		model.setSubject(tradeOrder.getBody());
		model.setOutTradeNo(tradeOrder.getOrderId());
		model.setTimeoutExpress(payCommonProperties.getAliPayConfig().getExpireTime() + "m");

		//分转成元 并且保留两位
		model.setTotalAmount(String.valueOf(new BigDecimal(tradeOrder.getAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)));
		model.setProductCode(goodsOrder.getGoodsId());
		model.setPassbackParams(PayUtil.getContextTenantId());
		try {
			log.info("[订单支付-请求支付宝-APP支付 - 统一收单] params:{}", JSON.toJSONString(model));
			AlipayTradeAppPayResponse alipayTradeAppPayResponse = AliPayApi.appPayToResponse(aliPayApiConfig.getAliPayClient(),
					model,payCommonProperties.getAliPayConfig().getNotifyUrl());
			log.info("[订单支付-请求支付宝-APP支付 - 统一收单] 返回:{}", JSON.toJSONString(alipayTradeAppPayResponse));
			/*AliPayApi.wapPay(response, model, payCommonProperties.getAliPayConfig().getReturnUrl()
					, payCommonProperties.getAliPayConfig().getNotifyUrl());*/
			return alipayTradeAppPayResponse;
		} catch (AlipayApiException e) {
			log.error("支付宝手机支付失败", e);
			tradeOrder.setErrMsg(e.getErrMsg());
			tradeOrder.setErrCode(e.getErrCode());
			tradeOrder.setStatus(OrderStatusEnum.FAIL.getStatus());
			goodsOrder.setStatus(OrderStatusEnum.FAIL.getStatus());
		}
		return null;
	}

	@Override
	public Integer payChannel() {
		return PayToolEnum.ALIPAY_WAP.value();
	}
}
