package com.eyuan.pay.handler.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.eyuan.pay.dto.TradeNoticeDTO;
import com.eyuan.pay.entity.PayNotifyRecord;
import com.eyuan.pay.handler.PayNotifyCallbackHandler;
import com.eyuan.pay.service.PayNotifyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-06-27
 */
@Slf4j
public abstract class AbstractPayNotifyCallbackHandler<T> implements PayNotifyCallbackHandler {
	@Autowired
	private PayNotifyRecordService payNotifyRecordService;

	/**
	 * 调用入口
	 *
	 * @param params
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String handle(Map<String, String> params) {

		//获取商户订单号
		String orderNo = getOrderNo(params);


		//查询订单并且初始化租户
		T data = getByOrderNoAndInit(orderNo);

		// 验签处理
		if (!verifyNotify(params)) {
			return null;
		}

		// 去重处理
		if (duplicateChecker(params)) {
			return null;
		}

		//幂等处理
		if (idempotentChecker(data)) {
			return buildResult(params);
		}

		TradeNoticeDTO tradeNoticeDTO = parse(params,data);
		//组装响应参数
		String result = buildResult(params);
		// 保存处理结果
		saveNotifyRecord(params, result);

		noticeSubSys(tradeNoticeDTO);
		return result;
	}

	protected abstract boolean idempotentChecker(T data);

	protected abstract String buildResult(Map<String, String> params);

	protected abstract TradeNoticeDTO parse(Map<String, String> params, T data);

	protected abstract T getByOrderNoAndInit(String orderNo);


	protected abstract String getOrderNo(Map<String, String> params);

	@Override
	public void before(Map<String, String> params) {

	}

	@Override
	public TradeNoticeDTO parse(Map<String, String> params) {
		return null;
	}

	@Override
	public void saveNotifyRecord(Map<String, String> params, String result) {
		PayNotifyRecord payNotifyRecord = new PayNotifyRecord();
		payNotifyRecord.setNotifyType(notifyType());
		payNotifyRecord.setNotifyId(getThirdOrderNo(params));
		payNotifyRecord.setOrderNo(getOrderNo(params));
		payNotifyRecord.setRequest(MapUtil.join(params, StrUtil.DASHED, StrUtil.DASHED));
		payNotifyRecord.setResponse(result);
		payNotifyRecordService.save(payNotifyRecord);
	}

	protected abstract String getThirdOrderNo(Map<String, String> params);
}
