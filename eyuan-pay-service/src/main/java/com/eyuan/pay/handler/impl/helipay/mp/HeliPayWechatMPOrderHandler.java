package com.eyuan.pay.handler.impl.helipay.mp;

import com.eyuan.pay.enums.PayToolEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * <p>
 * 合利宝微信公众号支付
 */
@Service
public class HeliPayWechatMPOrderHandler extends AbstractHeliPayMPOrderHandler {
	@Override
	public Integer payChannel() {
		return PayToolEnum.HELIPAY_WEIXIN_MP.value();
	}

	@Override
	protected PayToolEnum getPayToolEnum() {
		return PayToolEnum.HELIPAY_WEIXIN_MP;
	}
}
