package com.eyuan.pay.handler.impl.wx;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.utils.OrderStatusEnum;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.enums.TradeType;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.WxPayApiConfig;
import com.ijpay.wxpay.model.UnifiedOrderModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 微信扫码支付
 * @date 2020-11-02 12:08
 */
@Slf4j
@Service
public class WeChatNativePayOrderHandler extends AbstractWxPayOrderHandler {

	@Autowired
	private HttpServletRequest request;
	@Autowired
	private PayCommonProperties payCommonProperties;

	@Override
	public PayTradeOrder createTradeOrder(PayGoodsOrder goodsOrder, WxPayApiConfig wxPayApiConfig) {
		PayTradeOrder payTradeOrder = tradeOrderMapper.selectOne(new LambdaQueryWrapper<PayTradeOrder>().eq(PayTradeOrder::getOrderId, goodsOrder.getPayOrderId()));
		if (Objects.nonNull(payTradeOrder)) {
			return payTradeOrder;
		}
		PayTradeOrder tradeOrder = new PayTradeOrder();
		tradeOrder.setOrderId(goodsOrder.getPayOrderId());
		tradeOrder.setAmount(goodsOrder.getAmount());
		tradeOrder.setChannelId(PayToolEnum.WEIXIN_NATIVE.name());
		tradeOrder.setChannelMchId(wxPayApiConfig.getMchId());
		tradeOrder.setClientIp(ServletUtil.getClientIP(request));
		tradeOrder.setCurrency("CNY");
		tradeOrder.setStatus(OrderStatusEnum.INIT.getStatus());
		tradeOrder.setBody(goodsOrder.getGoodsName());
		tradeOrderMapper.insert(tradeOrder);
		return tradeOrder;
	}

	@Override
	public Object pay(PayGoodsOrder goodsOrder, PayTradeOrder tradeOrder,WxPayApiConfig wxPayApiConfig) {
		String ip = ServletUtil.getClientIP(request);
		Map<String, String> params = UnifiedOrderModel.builder().appid(wxPayApiConfig.getAppId()).mch_id(wxPayApiConfig.getMchId()).attach(getTenantId()).body(goodsOrder.getGoodsName())
				.spbill_create_ip(ip).total_fee(goodsOrder.getAmount()).openid(goodsOrder.getUserId())
				.trade_type(TradeType.NATIVE.getTradeType()).notify_url(payCommonProperties.getWxPayConfig()
						.getNotifyUrl()).out_trade_no(tradeOrder.getOrderId()).build().createSign(wxPayApiConfig.getPartnerKey(), SignType.HMACSHA256);

		log.info("[订单支付-请求微信-扫码（主扫）支付 - 统一收单] 请求appId:{}, params:{}", wxPayApiConfig.getAppId(), JSON.toJSONString(params));
		String xmlResult = WxPayApi.pushOrder(false, params);
		log.info("[订单支付-请求微信-扫码（主扫）支付 - 统一收单] 返回:{}", xmlResult);
		Map<String, String> resultMap = WxPayKit.xmlToMap(xmlResult);
		// 判断返回的结果
		String returnCode = resultMap.get("return_code");
		String returnMsg = resultMap.get("return_msg");
		AssertUtil.isTrue(WxPayKit.codeIsOk(returnCode), returnMsg);

        return resultMap.get("prepay_id");
	}

	@Override
	public Integer payChannel() {
		return PayToolEnum.WEIXIN_NATIVE.value();
	}
}
