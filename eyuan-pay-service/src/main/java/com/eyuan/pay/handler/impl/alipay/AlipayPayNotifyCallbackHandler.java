package com.eyuan.pay.handler.impl.alipay;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eyuan.pay.constant.PayConstants;
import com.eyuan.pay.dto.TradeNoticeDTO;
import com.eyuan.pay.entity.PayChannelConfig;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayChannelEnum;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.enums.TradeNotifyTypeEnum;
import com.eyuan.pay.facade.PayResultNoticeSubSystemFacade;
import com.eyuan.pay.handler.MessageDuplicateCheckerHandler;
import com.eyuan.pay.handler.impl.AbstractPayNotifyCallbackHandler;
import com.eyuan.pay.service.PayChannelConfigService;
import com.eyuan.pay.service.PayGoodsOrderService;
import com.eyuan.pay.service.PayTradeOrderService;
import com.eyuan.pay.utils.OrderStatusEnum;
import com.eyuan.pay.utils.PayUtil;
import com.eyuan.pay.utils.TradeStatusEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-27
 * <p>
 * 支付宝回调处理
 */
@Slf4j
@AllArgsConstructor
@Service("alipayCallback")
public class AlipayPayNotifyCallbackHandler extends AbstractPayNotifyCallbackHandler<PayGoodsOrder> {
	private final MessageDuplicateCheckerHandler duplicateCheckerHandler;
	private final PayTradeOrderService tradeOrderService;
	private final PayGoodsOrderService goodsOrderService;
	private final PayChannelConfigService payChannelConfigService;
	private final PayResultNoticeSubSystemFacade payResultNoticeSubSystemFacade;

	/**
	 * 去重处理
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean duplicateChecker(Map<String, String> params) {
		//判断是否是为支付中
		if (StrUtil.equals(TradeStatusEnum.WAIT_BUYER_PAY.getDescription(), params.get(PayConstants.TRADE_STATUS))) {
			log.info("支付宝订单待支付 {} 不做处理", params);
			return true;
		}

		// 判断10秒内是否已经回调处理
		if (duplicateCheckerHandler.isDuplicate(params.get(PayConstants.OUT_TRADE_NO))) {
			log.info("支付宝订单重复回调 {} 不做处理", params);
			this.saveNotifyRecord(params, "重复回调");
			return true;
		}
		return false;
	}

	/**
	 * 验签逻辑
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean verifyNotify(Map<String, String> params) {
		String callReq = MapUtil.join(params, StrUtil.DASHED, StrUtil.DASHED);
		log.info("支付宝发起回调 报文: {}", callReq);
		String appId = params.get("app_id");

		if (StrUtil.isBlank(appId)) {
			log.warn("支付宝回调报文 appid 为空 {}", callReq);
			return false;
		}
		PayChannelConfig payChannelConfig = payChannelConfigService.getByChannelId(PayChannelEnum.ALIPAY.getValue());
		String verifySignKey = payChannelConfig.getMerchantKey();
		if (StrUtil.isNotBlank(verifySignKey)) {
			log.warn("支付宝回调报文 appid 不合法 {}", callReq);
			return false;
		}
		
		try {
			return AlipaySignature.rsaCheckV1(params, verifySignKey
					, CharsetUtil.UTF_8, "RSA2");
		} catch (AlipayApiException e) {
			log.error("支付宝验签失败", e);
			return false;
		}
	}

	@Override
	protected String getThirdOrderNo(Map<String, String> params) {
		return params.get("notify_id");
	}

	@Override
	public void noticeSubSys(TradeNoticeDTO params) {
		payResultNoticeSubSystemFacade.noticeSubSystem(params);
	}

	@Override
	public String notifyType() {
		return TradeNotifyTypeEnum.PAY_NOTIFY.value();
	}

	@Override
	protected boolean idempotentChecker(PayGoodsOrder data) {
		return OrderStatusEnum.SUCCESS.getStatus().equals(data.getStatus());
	}

	@Override
	protected String buildResult(Map<String, String> params) {
		return "success";
	}

	/**
	 * 解析报文
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	protected TradeNoticeDTO parse(Map<String, String> params, PayGoodsOrder data) {
		String tradeStatus = EnumUtil.fromString(TradeStatusEnum.class, params.get(PayConstants.TRADE_STATUS)).getStatus();

		String orderNo = params.get(PayConstants.OUT_TRADE_NO);
		PayGoodsOrder goodsOrder = goodsOrderService.getOne(Wrappers.<PayGoodsOrder>lambdaQuery()
				.eq(PayGoodsOrder::getPayOrderId, orderNo));
		goodsOrder.setStatus(tradeStatus);
		goodsOrderService.updateById(goodsOrder);
		PayUtil.setContextTenantId(goodsOrder.getTenantId());

		PayTradeOrder tradeOrder = tradeOrderService.getOne(Wrappers.<PayTradeOrder>lambdaQuery()
				.eq(PayTradeOrder::getOrderId, orderNo));
		Long succTime = MapUtil.getLong(params, "time_end");
		tradeOrder.setPaySuccTime(succTime);
		tradeOrder.setChannelOrderNo(params.get("trade_no"));
		tradeOrder.setStatus(TradeStatusEnum.TRADE_SUCCESS.getStatus());
//		tradeOrder.setChannelOrderNo(params.get("transaction_id"));
		tradeOrderService.updateById(tradeOrder);

		PayToolEnum payToolEnum = PayToolEnum.valueOf(tradeOrder.getChannelId());
		TradeNoticeDTO tradeNoticeDTO = TradeNoticeDTO.builder().amount(BigDecimal.valueOf(Long.parseLong(tradeOrder.getAmount())).divide(BigDecimal.valueOf(100)))
				.tradeNo(orderNo).outTradeNo(tradeOrder.getChannelOrderNo()).tradeStatus(true).tenantId(tradeOrder.getTenantId())
				.payChannel(payToolEnum.value()).platformType(goodsOrder.getPlatformType()).build();
		return tradeNoticeDTO;
	}

	@Override
	protected PayGoodsOrder getByOrderNoAndInit(String orderNo) {
		PayGoodsOrder goodsOrder = goodsOrderService.getByPayOrderId(orderNo);
		if (Objects.isNull(goodsOrder)) {
			log.info("[支付宝支付回调], 商品订单信息为空, 订单号:{}", orderNo);
		}
		PayUtil.setContextTenantId(goodsOrder.getTenantId());
		return goodsOrder;
	}

	@Override
	protected String getOrderNo(Map<String, String> params) {
		return params.get(PayConstants.OUT_TRADE_NO);
	}
}
