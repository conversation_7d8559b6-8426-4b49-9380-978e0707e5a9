package com.eyuan.pay.handler.impl.helipay;

import cn.hutool.json.JSONUtil;
import com.dog.common.core.util.AssertUtil;
import com.dtflys.forest.Forest;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayRefundOrder;
import com.eyuan.pay.enums.PayChannelEnum;
import com.eyuan.pay.handler.impl.AbstractRefundOrderHandler;
import com.eyuan.pay.helipay.client.HeliAppPayClient;
import com.eyuan.pay.helipay.constant.HeliPayConstant;
import com.eyuan.pay.helipay.enums.BizTypeEnum;
import com.eyuan.pay.helipay.request.AppPayRefundOrderRequest;
import com.eyuan.pay.helipay.response.AppPayRefundOrderResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.helipay.util.HttpUtils;
import com.eyuan.pay.response.OrderRefundResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 合利宝退款
 * @date 2020-08-28 11:46
 */
@Slf4j
@Service
public class HeliPayAppletRefundOrderHandler extends AbstractRefundOrderHandler {

	@Resource
	private HeliAppPayClient heliAppPayClient;
	@Resource
	private PayCommonProperties payCommonProperties;


	@Override
	public OrderRefundResponse refund(PayRefundOrder payRefundOrder, PaySceneDTO paySceneDTO) {
//		String contextTenantId = PayUtil.getContextTenantId();
//		String tenantIdInfo = contextTenantId + PayChannelNameEnum1.WEIXIN_MP.getName();
//		String appId = PayUtil.getAppIdFromLocal(tenantIdInfo);
//		log.info("[支付] tenantIdInfo:{}, appId:{}", tenantIdInfo, appId);
//		String wxApiConfigKey = contextTenantId + PayConstants.WX_API_CONFIG_JOIN + appId;
//		WxPayApiConfig wxPayApiConfig = WxPayApiConfigKit.getApiConfig(wxApiConfigKey);

		AppPayRefundOrderRequest appPayRefundOrderRequest = new AppPayRefundOrderRequest();
		appPayRefundOrderRequest.setP1_bizType(BizTypeEnum.AppPayRefund.name());
		appPayRefundOrderRequest.setP2_orderId(payRefundOrder.getPayOrderId());
		appPayRefundOrderRequest.setP3_customerNumber(paySceneDTO.getMerchantId());
		appPayRefundOrderRequest.setP4_refundOrderId(payRefundOrder.getMchRefundNo());
		BigDecimal refundAmount = new BigDecimal(payRefundOrder.getRefundAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

		appPayRefundOrderRequest.setP5_amount(String.valueOf(refundAmount));
		appPayRefundOrderRequest.setP6_callbackUrl(payCommonProperties.getHeliPayConfig().getRefundNotifyUrl());
//		log.info("[订单退款-请求合利宝 - 申请退款] 请求appId:{}, mchId:{}, paternerKey:{}, params:{}",
//				appId, wxPayApiConfig.getMchId(), wxPayApiConfig.getPartnerKey(), JSON.toJSONString(appPayRefundOrderRequest));
		Map<String, String> requestMap = HeliSignatureUtils.convertRequestAndCreateSign(appPayRefundOrderRequest,
				AppPayRefundOrderRequest.NEED_SIGN_PARAMS, AppPayRefundOrderRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
		String result = HttpUtils.doExecute(payCommonProperties.getHeliPayConfig().getPayUrl(), requestMap);
		AppPayRefundOrderResponse appPayRefundOrderResponse = JSONUtil.toBean(result, AppPayRefundOrderResponse.class);
//		AppPayRefundOrderResponse appPayRefundOrderResponse = heliAppPayClient.appPayAppletRefund(payCommonProperties.getHeliPayConfig().getPayUrl(),requestMap);
		log.info("[订单退款-请求合利宝 - 申请退款] 返回:{}", JSONUtil.toJsonStr(appPayRefundOrderResponse));
		AssertUtil.isTrue(HeliPayConstant.SUCCESS_RETURN_RECEIVE_CODE.equals(appPayRefundOrderResponse.getRt2_retCode()), appPayRefundOrderResponse.getRt3_retMsg());
		return OrderRefundResponse.builder().payOrderId(payRefundOrder.getPayOrderId()).mchRefundNo(payRefundOrder.getMchRefundNo())
				.outRefundNo(appPayRefundOrderResponse.getRt7_serialNumber()).build();	}

	@Override
	public String payChannel() {
		return PayChannelEnum.HELIPAY.value();
	}

	/**
	 * appId:wx506612d0c0640d99, mchId:1618163059, paternerKey:OvlrIwhvEAHN5W4VN7qDkirdcsNShqwj,
	 * params:{"transaction_id":"4200001364202204070570976744","nonce_str":"4zycsqjb2xmgh4c0uh7d7hxvgikmrld7",
	 * "out_refund_no":"hr001512271997743271936","appid":"wx506612d0c0640d99","total_fee":"1","refund_fee":"1",
	 * "sign":"8DBBB9E4B1C30945143F11D3CC58A9BC","mch_id":"1618163059"}, certPath is:/root/cert/45/apiclient_cert.p12, certKey:1618163059
	 * @param args
	 */
	public static void main(String[] args) {
		AppPayRefundOrderRequest appPayRefundOrderRequest = new AppPayRefundOrderRequest();
		appPayRefundOrderRequest.setP1_bizType(BizTypeEnum.AppPayRefund.name());
		appPayRefundOrderRequest.setP3_customerNumber(HeliPayConstant.MERCHANT_NO);
		appPayRefundOrderRequest.setP4_refundOrderId("122232323232");
		appPayRefundOrderRequest.setP5_amount(String.valueOf("1"));
		log.info("[订单退款-请求合利宝 - 申请退款] 请求参数:{}",JSONUtil.toJsonStr(appPayRefundOrderRequest));
		Map<String, String> requestMap = HeliSignatureUtils.convertRequestAndCreateSign(appPayRefundOrderRequest,
				AppPayRefundOrderRequest.NEED_SIGN_PARAMS, AppPayRefundOrderRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
		Object execute = Forest.post("http://test.trx.helipay.com/trx/app/interface.action").addBody(requestMap).execute();
		System.out.println(JSONUtil.toJsonStr(execute));
	}

}
