package com.eyuan.pay.handler.impl.helipay;

import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.enums.PayTypeEnum;
import com.eyuan.pay.handler.impl.AbstractPayOrderHandler;
import com.eyuan.pay.helipay.enums.AppPayType;

/**
 * 合利宝支付抽象处理
 * @Author: sunwh
 * @Date: 2025/5/16 11:05
 */
public abstract class AbstractHeliPayOrderHandler extends AbstractPayOrderHandler<PaySceneDTO> {

    @Override
    protected PaySceneDTO convertConfig(PaySceneDTO paySceneDTO) {
        return paySceneDTO;
    }

    protected abstract PayToolEnum getPayToolEnum();

    protected String getAppPayType() {
        String payTypeValue = getPayToolEnum().payTypeValue();
        if (PayTypeEnum.WXPAY.value().equals(payTypeValue)) {
            return AppPayType.WXPAY.name();
        }
        if (PayTypeEnum.ALIPAY.value().equals(payTypeValue)) {
            return AppPayType.ALIPAY.name();
        }
        return null;
    }
}
