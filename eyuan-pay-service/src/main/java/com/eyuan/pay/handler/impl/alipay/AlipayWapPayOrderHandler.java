package com.eyuan.pay.handler.impl.alipay;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.utils.OrderStatusEnum;
import com.ijpay.alipay.AliPayApi;
import com.ijpay.alipay.AliPayApiConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2019-05-31
 * <p>
 * 支付宝手机支付
 */
@Slf4j
@Service
@AllArgsConstructor
public class AlipayWapPayOrderHandler extends AbstractAliPayOrderHandler {
	private final PayCommonProperties payCommonProperties;
	private final HttpServletRequest request;
	private final HttpServletResponse response;

	/**
	 * 创建交易订单
	 *
	 * @param goodsOrder
	 * @return
	 */
	@Override
	public PayTradeOrder createTradeOrder(PayGoodsOrder goodsOrder, AliPayApiConfig aliPayApiConfig) {
		PayTradeOrder payTradeOrder = tradeOrderMapper.selectOne(Wrappers.<PayTradeOrder>lambdaQuery().eq(PayTradeOrder::getOrderId, goodsOrder.getPayOrderId()));
		if (Objects.nonNull(payTradeOrder)) {
			log.info("[支付] 支付宝, 重复交易订单, payOrderId:{}", goodsOrder.getPayOrderId());
			return payTradeOrder;
		}
		PayTradeOrder tradeOrder = new PayTradeOrder();
		tradeOrder.setOrderId(goodsOrder.getPayOrderId());
		tradeOrder.setAmount(goodsOrder.getAmount());
		tradeOrder.setChannelId(PayToolEnum.ALIPAY_WAP.name());
		tradeOrder.setChannelMchId(aliPayApiConfig.getAppId());
		tradeOrder.setClientIp(ServletUtil.getClientIP(request));
		tradeOrder.setCurrency("cny");
		tradeOrder.setExpireTime(Long.parseLong(payCommonProperties.getAliPayConfig().getExpireTime()));
		tradeOrder.setStatus(OrderStatusEnum.INIT.getStatus());
		tradeOrder.setBody(goodsOrder.getGoodsName());
		tradeOrderMapper.insert(tradeOrder);
		return tradeOrder;
	}

	/**
	 * 调起渠道支付
	 *
	 * @param goodsOrder 商品订单
	 * @param tradeOrder 交易订单
	 */
	@Override
	public String pay(PayGoodsOrder goodsOrder, PayTradeOrder tradeOrder, AliPayApiConfig aliPayApiConfig) {
		AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
		model.setBody(tradeOrder.getBody());
		model.setSubject(tradeOrder.getBody());
		model.setOutTradeNo(tradeOrder.getOrderId());
		model.setTimeoutExpress(payCommonProperties.getAliPayConfig().getExpireTime() + "m");
		
		// 修改金额转换逻辑
		BigDecimal amount = new BigDecimal(tradeOrder.getAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
		model.setTotalAmount(amount.toString());
		
		model.setProductCode("QUICK_WAP_WAY");
		try {
			log.info("[订单支付-请求支付宝-付款码支付 - 统一收单] params:{}", JSON.toJSONString(model));
			String result = AliPayApi.wapPayStr(aliPayApiConfig.getAliPayClient(), model,
					payCommonProperties.getAliPayConfig().getReturnUrl(),
					payCommonProperties.getAliPayConfig().getNotifyUrl());
			log.info("[订单支付-请求支付宝-付款码支付 - 统一收单] 返回:{}", result);
			return result;
		} catch (AlipayApiException e) {
			log.error("支付宝WAP支付失败", e);
			tradeOrder.setErrMsg(e.getMessage());
			tradeOrder.setStatus(OrderStatusEnum.FAIL.getStatus());
			goodsOrder.setStatus(OrderStatusEnum.FAIL.getStatus());
		}
		return null;
	}

	@Override
	public Integer payChannel() {
		return PayToolEnum.ALIPAY_WAP.value();
	}
}
