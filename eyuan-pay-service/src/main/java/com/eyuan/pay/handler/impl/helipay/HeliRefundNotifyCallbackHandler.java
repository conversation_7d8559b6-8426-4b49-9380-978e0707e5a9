package com.eyuan.pay.handler.impl.helipay;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.eyuan.pay.dto.TradeNoticeDTO;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayRefundOrder;
import com.eyuan.pay.enums.PayRefundStatusEnum;
import com.eyuan.pay.enums.TradeNotifyTypeEnum;
import com.eyuan.pay.facade.PayResultNoticeSubSystemFacade;
import com.eyuan.pay.handler.MessageDuplicateCheckerHandler;
import com.eyuan.pay.handler.impl.AbstractPayNotifyCallbackHandler;
import com.eyuan.pay.helipay.enums.HeliPayTradeStatusEnum;
import com.eyuan.pay.helipay.response.HeliRefundNotifyResponse;
import com.eyuan.pay.helipay.response.HeliTransferNotifyResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.service.PayGoodsOrderService;
import com.eyuan.pay.service.PayRefundOrderService;
import com.eyuan.pay.utils.PayUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-27
 * <p>
 * 合利宝回调处理
 */
@Slf4j
@AllArgsConstructor
@Service("heliRefundCallback")
public class HeliRefundNotifyCallbackHandler extends AbstractPayNotifyCallbackHandler<PayRefundOrder> {
	private final MessageDuplicateCheckerHandler duplicateCheckerHandler;
	private final PayRefundOrderService payRefundOrderService;
	private final PayResultNoticeSubSystemFacade payResultNoticeSubSystemFacade;
	private final PayGoodsOrderService payGoodsOrderService;


	/**
	 * 去重处理
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean duplicateChecker(Map<String, String> params) {
		String orderNo = getOrderNo(params);
		// 判断10秒内是否已经回调处理
		if (duplicateCheckerHandler.isDuplicate(orderNo)) {
			log.info("[合利宝退款回调] 重复回调, 不做处理, params:{}", params);
			this.saveNotifyRecord(params, "重复回调");
			return true;
		}
		return false;
	}

	/**
	 * 验签逻辑
	 *
	 * @param params 回调报文
	 * @return
	 */
	@Override
	public Boolean verifyNotify(Map<String, String> params) {
		boolean verify = HeliSignatureUtils.verifySignByRes(params, HeliRefundNotifyResponse.NEED_SIGN_PARAMS);
		if (!verify) {
			log.warn("[合利宝退款回调] 回调验签失败");
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 解析报文
	 *
	 * @param params
	 * @return
	 */
	@Override
	public TradeNoticeDTO parse(Map<String, String> params, PayRefundOrder payRefundOrder) {
		log.info("[合利宝退款回调], 参数解析:{}", JSON.toJSONString(params));
		String status = params.get("rt5_status");
		Date successTime = MapUtil.getDate(params, "rt8_timestamp");
		if(HeliPayTradeStatusEnum.SUCCESS.getDescription().equals(status)){
			PayRefundOrder updatePayRefundOrder = new PayRefundOrder();
			updatePayRefundOrder.setRefundOrderId(payRefundOrder.getRefundOrderId());
			updatePayRefundOrder.setStatus(PayRefundStatusEnum.REFUND_SUCCESS.getValue());
			updatePayRefundOrder.setRefundSuccTime(DateUtil.toLocalDateTime(successTime));
			payRefundOrderService.updateById(updatePayRefundOrder);
		} else {
			PayRefundOrder updatePayRefundOrder = new PayRefundOrder();
			updatePayRefundOrder.setRefundOrderId(payRefundOrder.getRefundOrderId());
			updatePayRefundOrder.setStatus(PayRefundStatusEnum.REFUND_FAILURE.getValue());
			payRefundOrderService.updateById(updatePayRefundOrder);
		}
		PayGoodsOrder payGoodsOrder = payGoodsOrderService.getByPayOrderId(payRefundOrder.getPayOrderId());
		TradeNoticeDTO tradeNoticeDTO = TradeNoticeDTO.builder().amount(BigDecimal.valueOf(payRefundOrder.getRefundAmount()).divide(BigDecimal.valueOf(100)))
				.tradeNo(getOrderNo(params)).outTradeNo(getThirdOrderNo(params)).tradeStatus(Objects.equals(HeliPayTradeStatusEnum.SUCCESS.getDescription(), status))
				.tenantId(payGoodsOrder.getTenantId()).tradeTime(successTime).platformType(payGoodsOrder.getPlatformType()).build();
		return tradeNoticeDTO;
	}

	@Override
	protected boolean idempotentChecker(PayRefundOrder data) {
		return PayRefundStatusEnum.REFUND_SUCCESS.getValue().equals(data.getStatus());
	}

	@Override
	protected String buildResult(Map<String, String> params) {
		return "success";
	}


	@Override
	protected String getThirdOrderNo(Map<String, String> params) {
		return params.get("rt4_systemSerial");
	}

	@Override
	public void noticeSubSys(TradeNoticeDTO params) {
		payResultNoticeSubSystemFacade.refundNoticeSubSystem(params);

	}

	@Override
	public String notifyType() {
		return TradeNotifyTypeEnum.REFUND_NOTIFY.value();
	}

	@Override
	protected PayRefundOrder getByOrderNoAndInit(String refundNo) {
		PayRefundOrder payRefundOrder = payRefundOrderService.getByRefundNo(refundNo);
		if (Objects.isNull(payRefundOrder)) {
			log.info("[合利宝退款回调], 退款信息为空, 订单号:{}", refundNo);
		}
		PayUtil.setContextTenantId(payRefundOrder.getTenantId());
		return payRefundOrder;
	}

	@Override
	protected String getOrderNo(Map<String, String> params) {
		return params.get("rt3_refundOrderId");
	}
}
