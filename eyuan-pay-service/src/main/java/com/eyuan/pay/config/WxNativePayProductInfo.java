package com.eyuan.pay.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 微信支付商品信息
 * @date 2020-10-31 16:27
 */
@Data
//@RefreshScope
//@Configuration
//@ConfigurationProperties(prefix = "wx.product")
public class WxNativePayProductInfo implements java.io.Serializable {

	private static final long serialVersionUID = 7578992060269789112L;

	// 商品id
	private String productid;

	// 二维码金额（单位：分；如1分钱则输入：1）
	private Long moneyAmount;

	// 商品名称
	private String productName;

}
