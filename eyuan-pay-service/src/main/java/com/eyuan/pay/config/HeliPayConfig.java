package com.eyuan.pay.config;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019-06-19
 */
@Data
@Component
public class HeliPayConfig {
	/**
	 * 支付请求地址
	 */
	private String payUrl;
	/**
	 * 代付请求地址
	 */
	private String transferUrl;

	/**
	 * 进件请求地址
	 */
	private String entryUrl;

	/**
	 * 商户SM2证书私钥路径
	 */
	private String privateKeyPath;

	/**
	 * SM2证书公钥路径
	 */
	private String publicKeyPath;

	/**
	 * 商户SM2证书私钥密码
	 */
	private String privateKeyPassword;

	/**
	 * 支付通知地址
	 */
	private String payNotifyUrl;

	/**
	 * 退款通知地址
	 */
	private String refundNotifyUrl;


	/**
	 * 代付、结算通知地址
	 */
	private String transferNotifyUrl;

//	/**
//	 * MD5签名密钥
//	 */
//	private String md5SignKey;
//
//	/**
//	 * 3DES加密密钥
//	 */
//	private String desSignKey;
}
