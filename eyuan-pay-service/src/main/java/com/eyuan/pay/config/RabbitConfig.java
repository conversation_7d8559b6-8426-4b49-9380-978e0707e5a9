package com.eyuan.pay.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@Slf4j
public class RabbitConfig {

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setConfirmCallback(new GlobalConfirmCallback());
        return template;
    }
    
    @Component
    public static class GlobalConfirmCallback implements RabbitTemplate.ConfirmCallback {

        @Override
        public void confirm(CorrelationData correlationData, boolean ack, String cause) {
            if (correlationData != null) {
                String messageId = correlationData.getId();
                //返回成功，表示消息被正常投递
                if (ack) {
                    log.info("消息投递成功, messageId: {}", messageId);
                } else {
                    log.error("消息投递失败, messageId: {}, 原因: {}", messageId, cause);
                }
            }
        }
    }
}