package com.eyuan.pay.config;

import com.eyuan.pay.handler.PayOrderHandler;
import com.eyuan.pay.handler.RefundOrderHandler;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务
 * @date 2020-08-04 17:25
 */
@Component
@Slf4j
public class HandlerLocator implements ApplicationContextAware {

	/**
	 * 用于保存接口实现类名及对应的类
	 */
	private static Map<Integer, PayOrderHandler> payOrderHandlerMap = Maps.newConcurrentMap();

	private static Map<String, RefundOrderHandler> refundOrderHandlerMap = Maps.newConcurrentMap();

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		applicationContext.getBeansOfType(PayOrderHandler.class).values().forEach(orderHandler -> {
			payOrderHandlerMap.put(orderHandler.payChannel(), orderHandler);
		});


		applicationContext.getBeansOfType(RefundOrderHandler.class).values().forEach(refundOrderHandler -> {
			refundOrderHandlerMap.put(refundOrderHandler.payChannel(), refundOrderHandler);
		});
	}

	public PayOrderHandler getPayOrderHandler(Integer payChannel) {
		return payOrderHandlerMap.get(payChannel);
	}

	public RefundOrderHandler getRefundOrderHandler(String payChannel) {
		return refundOrderHandlerMap.get(payChannel);
	}
}
