package com.eyuan.pay.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @description 微信扫码支付商品配置
 * @date 2020-10-31 16:34
 */
@Configuration
@ConfigurationProperties(prefix = "wx.product")
@Data
@Slf4j
public class WxNativePayProductInfoConfig {

	private List<WxNativePayProductInfo> productInfoList;

	public WxNativePayProductInfo getProductInfo(String productid) {
		WxNativePayProductInfo wxNativePayProductInfo = productInfoList.stream()
				.filter(productInfo -> productInfo.getProductid().equals(productid))
				.findFirst().orElseThrow(() -> new RuntimeException("未找到对应的商品信息"));
		return wxNativePayProductInfo;
	}

}
