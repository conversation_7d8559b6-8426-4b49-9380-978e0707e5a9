//package com.eyuan.pay.config;
//
//import cn.hutool.core.lang.Assert;
//import cn.hutool.core.util.CharsetUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.annotation.SqlParser;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.dog.admin.api.feign.RemoteTenantService;
//import com.dog.common.core.constant.CommonConstants;
//import com.dog.common.core.constant.SecurityConstants;
//import com.eyuan.pay.constant.PayConstants;
//import com.eyuan.pay.entity.PayChannel;
//import com.eyuan.pay.service.PayChannelService;
//import com.eyuan.pay.utils.PayUtil;
//import com.eyuan.pay.enums.PayChannelNameEnum;
//import com.ijpay.alipay.AliPayApiConfig;
//import com.ijpay.alipay.AliPayApiConfigKit;
//import com.ijpay.wxpay.WxPayApiConfig;
//import com.ijpay.wxpay.WxPayApiConfigKit;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import me.chanjar.weixin.mp.api.WxMpService;
//import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
//import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
//import org.springframework.boot.web.context.WebServerInitializedEvent;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.event.EventListener;
//import org.springframework.core.annotation.Order;
//import org.springframework.scheduling.annotation.Async;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date 2019-05-31
// * <p>
// * 支付参数初始化
// */
//@Slf4j
//@Configuration
//@AllArgsConstructor
//public class PayConfigParmaInitRunner {
//	public static Map<String, WxMpService> mpServiceMap = new HashMap<>();
//	private final PayChannelService channelService;
//	private final RemoteTenantService tenantService;
//
//
//	@Async
//	@Order
//	@SqlParser(filter = true)
//	@EventListener({WebServerInitializedEvent.class})
//	public void initPayConfig() {
//
//		List<PayChannel> channelList = new ArrayList<>();
//		tenantService.list(SecurityConstants.FROM_IN).getData()
//				.forEach(tenant -> {
////					TenantContextHolder.setTenantId(tenant.getId());
//					PayUtil.setContextTenantId(tenant.getId());
//					LambdaQueryWrapper<PayChannel> wrapper = Wrappers.<PayChannel>lambdaQuery().eq(PayChannel::getState, CommonConstants.STATUS_NORMAL);
//					List<PayChannel> payChannelList = channelService.list(wrapper);
////					payChannelList = channelService.listPayChannel(wrapper);
//					channelList.addAll(payChannelList);
//				});
//		PayUtil.clearContextTenantId();
//
//		channelList.forEach(channel -> {
//			addPayConfig(channel);
//		});
//
//		log.info("[初始化支付参数] mapInfo:{}", JSON.toJSONString(PayUtil.getTenantAppIdMap()));
//	}
//
//	public void addPayConfig(PayChannel channel) {
//		JSONObject params = JSONUtil.parseObj(channel.getParam());
//
//		Assert.notBlank(channel.getTenantId(), "初始化参数错误，租户id不能为空");
//		Assert.notBlank(channel.getAppId(), "初始化参数错误，AppId不能为空");
//		Assert.notBlank(channel.getChannelId(), "初始化参数错误，channelId不能为空");
//
//		// 设置 租户id和appId到本地缓存
//		PayUtil.setAppIdToLocal(channel.getTenantId()+ channel.getChannelId(), channel.getAppId());
//
//		//支付宝支付
//		if (PayChannelNameEnum.ALIPAY_WAP.getName().equals(channel.getChannelId())) {
//
//			AliPayApiConfig aliPayApiConfig = AliPayApiConfig.New()
//					.setAppId(channel.getAppId())
//					.setPrivateKey(params.getStr("privateKey"))
//					.setCharset(CharsetUtil.UTF_8)
//					.setAlipayPublicKey(params.getStr("publicKey"))
//					.setServiceUrl(params.getStr("serviceUrl"))
//					.setSignType("RSA2")
//					.build();
//
//			AliPayApiConfigKit.putApiConfig(aliPayApiConfig);
//		}
//		// 微信支付
//		if (PayChannelNameEnum.WEIXIN_MP.getName().equals(channel.getChannelId()) ||
//				PayChannelNameEnum.WEIXIN_PAYMENT_CODE.getName().equals(channel.getChannelId())) {
//			//服务商模式存在多个相同的appId,所以appId不能作为唯一标识，这里增加tenantId+"|"作为前缀
//			String appId = channel.getTenantId() + PayConstants.WX_API_CONFIG_JOIN + channel.getAppId();
//			WxPayApiConfig wx = WxPayApiConfig.New()
//					.setAppId(appId)
//					.setMchId(channel.getChannelMchId())
//					.setPaternerKey(params.getStr("paternerKey"))
//					.setPayModel(WxPayApiConfig.PayModel.BUSINESSMODEL);
//
//			String subMchId = params.getStr("subMchId");
//			if (StrUtil.isNotBlank(subMchId)) {
//				wx.setPayModel(WxPayApiConfig.PayModel.SERVICEMODE);
//				wx.setSubMchId(subMchId);
//			}
//
//			String subAppId = params.getStr("subAppId");
//			if (StrUtil.isNotBlank(subAppId)) {
//				wx.setSubAppId(subAppId);
//			}
//
//			WxPayApiConfigKit.putApiConfig(wx);
//
//			WxMpService wxMpService = new WxMpServiceImpl();
//			WxMpDefaultConfigImpl storage = new WxMpDefaultConfigImpl();
//			storage.setAppId(appId);
//			storage.setSecret(params.getStr("secret"));
//			storage.setToken(params.getStr("token"));
//			wxMpService.setWxMpConfigStorage(storage);
//
//			mpServiceMap.put(appId, wxMpService);
//		}
//
//		if (PayChannelNameEnum.HELI_WEIXIN_APPLET.getName().equals(channel.getChannelId())) {
//			String appId = channel.getTenantId() + PayConstants.WX_API_CONFIG_JOIN + channel.getAppId();
//			WxPayApiConfig wx = WxPayApiConfig.New()
//					.setAppId(appId)
//					.setMchId(channel.getChannelMchId());
//			WxPayApiConfigKit.putApiConfig(wx);
//		}
//	}
//
//	public void putPayConfig(PayChannel channel) {
//		removePayConfig(channel);
//		addPayConfig(channel);
//	}
//
//	public void removePayConfig(PayChannel channel) {
//		if (PayChannelNameEnum.ALIPAY_WAP.getName().equals(channel.getChannelId())) {
//			AliPayApiConfigKit.removeApiConfig(channel.getAppId());
//		}
//		if (PayChannelNameEnum.WEIXIN_MP.getName().equals(channel.getChannelId())) {
//			//服务商模式存在多个相同的appId,所以appId不能作为唯一标识，这里增加tenantId+"|"作为前缀
//			String appId = channel.getTenantId() + PayConstants.WX_API_CONFIG_JOIN + channel.getAppId();
//			WxPayApiConfigKit.removeApiConfig(appId);
//		}
//	}
//}
