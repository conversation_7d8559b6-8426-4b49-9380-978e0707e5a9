package com.eyuan.pay.utils;

import com.dog.common.core.context.TenantHolder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 支付系统工具
 * @date 2020-08-04 16:47
 */
public class PayUtil {

	private final static Map<String, String> TENANT_APP_ID_MAP = new HashMap<>();

	public static String getWxAppIdFromLocal(String channelId) {
		String tenantIdInfo = PayUtil.getContextTenantId() + channelId;
		return TENANT_APP_ID_MAP.get(tenantIdInfo);
	}

	public static String getAppIdFromLocal(String tenantIdInfo) {
		return TENANT_APP_ID_MAP.get(tenantIdInfo);
	}

	public static void setContextTenantId(Integer tenant) {
		TenantHolder.setTenantId(String.valueOf(tenant));
	}

	public static void setContextTenantId(String tenantId) {
		TenantHolder.setTenantId(tenantId);
	}

	public static String getContextTenantId() {
		return TenantHolder.getTenantId();
	}

	public static void clearContextTenantId() {
		TenantHolder.clear();
	}

	public static Date getDate(LocalDateTime localDateTime) {
		return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}
}
