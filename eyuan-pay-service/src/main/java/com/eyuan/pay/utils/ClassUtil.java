package com.eyuan.pay.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @description class 工具类
 * @date 2020-11-02 11:10
 */
@Slf4j
public class ClassUtil {

	public static Map<String, Object> objectToMap(Object object) {
		Field[] declaredFields = object.getClass().getDeclaredFields();
		Map<String, Object> map = new HashMap<>();
		for (Field declaredField : declaredFields) {
			if (declaredField.getName().equals("serialVersionUID")) {
				continue;
			}
			declaredField.setAccessible(true);
			try {
				Object value = declaredField.get(object);
				if (value != null) {
					map.put(declaredField.getName(), value);
				}
			} catch (IllegalAccessException e) {
				log.error("[objectToMap 转换失败], err:", e);
			}
		}
		return map;
	}

	public static <T> T mapToObject(Map<String, String> params, Class<T> clazz) {
		T object;
		try {
			object = clazz.newInstance();
		} catch (Exception e) {
			throw new RuntimeException("java.lang.Class#newInstance error, e:", e);
		}
		Set<String> keySet = params.keySet();
		for (String name : keySet) {
			Field classField = getClassField(clazz, name);
			if (Objects.isNull(classField)) {
				continue;
			}
			classField.setAccessible(true);
			try {
				classField.set(object, params.get(name));
			} catch (IllegalAccessException e) {
				log.error("java.lang.reflect.Field#set error, e:", e);
			}
		}
		return object;
	}

	private static Field getClassField(Class<?> clazz, String propertyName) {
		Field[] declaredFields = clazz.getDeclaredFields();
		for (Field field : declaredFields) {
			if (field.getName().equals(propertyName)) {
				return field;
			}
		}
		return null;
	}

}
