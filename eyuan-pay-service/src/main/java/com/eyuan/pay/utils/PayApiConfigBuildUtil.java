package com.eyuan.pay.utils;

import cn.hutool.core.util.CharsetUtil;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.enums.PayModeEnum;
import com.ijpay.alipay.AliPayApiConfig;
import com.ijpay.wxpay.WxPayApiConfig;

/**
 * @Author: sunwh
 * @Date: 2025/5/16 10:24
 */
public class PayApiConfigBuildUtil {

    /**
     * 构建支付宝支付配置
     * @param paySceneDTO
     * @return
     */
    public static AliPayApiConfig buildAliPayApiConfig(PaySceneDTO paySceneDTO) {
        return AliPayApiConfig.builder()
                .setAppId(paySceneDTO.getAppId())
                .setPrivateKey(paySceneDTO.getPrivateKey())
                .setCharset(CharsetUtil.UTF_8)
                .setAliPayPublicKey(paySceneDTO.getPublicKey())
                .setSignType("RSA2")
                .build();
    }

    /**
     * 构建微信支付配置
     * @param paySceneDTO
     * @return
     */
    public static WxPayApiConfig buildWxPayApiConfig(PaySceneDTO paySceneDTO) {
        WxPayApiConfig wx = WxPayApiConfig.builder()
                .partnerKey(paySceneDTO.getMerchantKey())
                .build();

        if(PayModeEnum.SERVICE.getValue().equals(paySceneDTO.getMode())){
            wx.setMchId(paySceneDTO.getSubMerchantId());
            wx.setAppId(paySceneDTO.getSubAppId());
            wx.setSlMchId(paySceneDTO.getMerchantId());
            wx.setSlAppId(paySceneDTO.getAppId());
        }  else {
            wx.setMchId(paySceneDTO.getMerchantId());
            wx.setAppId(paySceneDTO.getAppId());
        }
        return wx;
    }


}
