package com.eyuan.pay.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.util.R;
import com.dog.common.log.annotation.SysLog;
import com.eyuan.pay.entity.PayNotifyRecord;
import com.eyuan.pay.handler.PayNotifyCallbackHandler;
import com.eyuan.pay.handler.impl.helipay.HeliPayNotifyCallbackHandler;
import com.eyuan.pay.handler.impl.helipay.HeliRefundNotifyCallbackHandler;
import com.eyuan.pay.handler.impl.helipay.HeliTransferNotifyCallbackHandler;
import com.eyuan.pay.helipay.response.HeliRefundNotifyResponse;
import com.eyuan.pay.helipay.response.HeliTransferNotifyResponse;
import com.eyuan.pay.helipay.response.NotifyResponse;
import com.eyuan.pay.helipay.util.HeliBeanUtils;
import com.eyuan.pay.service.PayNotifyRecordService;
import com.ijpay.alipay.AliPayApi;
import com.ijpay.core.kit.HttpKit;
import com.ijpay.core.kit.WxPayKit;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * 异步通知记录
 *
 * <AUTHOR>
 * @date 2019-05-28 23:57:23
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/rest/front/notify")
@Api(value = "notify", tags = "notify管理")
public class PayNotifyRecordController {
	private final PayNotifyRecordService payNotifyRecordService;
	private final PayNotifyCallbackHandler alipayCallback;
	private final PayNotifyCallbackHandler weChatCallback;
	private final PayNotifyCallbackHandler weChatRefundCallback;
	private final HeliPayNotifyCallbackHandler heliPayNotifyCallbackHandler;
	private final HeliRefundNotifyCallbackHandler heliRefundNotifyCallbackHandler;
	private final HeliTransferNotifyCallbackHandler heliTransferNotifyCallbackHandler;

	/**
	 * 分页查询
	 *
	 * @param page            分页对象
	 * @param payNotifyRecord 异步通知记录
	 * @return
	 */
	@GetMapping("/page")
	public R getPayNotifyRecordPage(Page page, PayNotifyRecord payNotifyRecord) {
		return R.ok(payNotifyRecordService.page(page, Wrappers.query(payNotifyRecord)));
	}


	/**
	 * 通过id查询异步通知记录
	 *
	 * @param id id
	 * @return R
	 */
	@GetMapping("/{id}")
	public R getById(@PathVariable("id") Long id) {
		return R.ok(payNotifyRecordService.getById(id));
	}

	/**
	 * 新增异步通知记录
	 *
	 * @param payNotifyRecord 异步通知记录
	 * @return R
	 */
	@SysLog("新增异步通知记录")
//	@PostMapping
//	@PreAuthorize("@pms.hasPermission('pay_paynotifyrecord_add')")
	public R save(@RequestBody PayNotifyRecord payNotifyRecord) {
		return R.ok(payNotifyRecordService.save(payNotifyRecord));
	}

	/**
	 * 修改异步通知记录
	 *
	 * @param payNotifyRecord 异步通知记录
	 * @return R
	 */
	@SysLog("修改异步通知记录")
//	@PutMapping
//	@PreAuthorize("@pms.hasPermission('pay_paynotifyrecord_edit')")
	public R updateById(@RequestBody PayNotifyRecord payNotifyRecord) {
		return R.ok(payNotifyRecordService.updateById(payNotifyRecord));
	}

	/**
	 * 通过id删除异步通知记录
	 *
	 * @param id id
	 * @return R
	 */
	@SysLog("删除异步通知记录")
//	@DeleteMapping("/{id}")
//	@PreAuthorize("@pms.hasPermission('pay_paynotifyrecord_del')")
	public R removeById(@PathVariable Long id) {
		return R.ok(payNotifyRecordService.removeById(id));
	}

	/**
	 * 支付宝渠道异步回调
	 *
	 * @param request 渠道请求
	 * @return
	 */
//	@Inner(false)
	@SneakyThrows
	@PostMapping("/ali/callbak")
	public void aliCallbak(HttpServletRequest request, HttpServletResponse response) {
		// 解析回调信息
		Map<String, String> params = AliPayApi.toMap(request);
		response.getWriter().print(alipayCallback.handle(params));
	}

	/**
	 * 微信渠道支付回调
	 *
	 * @param request
	 * @return
	 */
//	@Inner(false)
	@SneakyThrows
	@ResponseBody
	@PostMapping("/wx/callbak")
	public String wxCallback(HttpServletRequest request) {
		log.info("[微信回调] 请求信息:{}", JSON.toJSONString(request.getParameterMap()));
		String xmlMsg = HttpKit.readData(request);
		log.info("[微信回调] 解析请求信息为xml:{}", xmlMsg);
		Map<String, String> params = WxPayKit.xmlToMap(xmlMsg);
		return weChatCallback.handle(params);
	}

	/**
	 * 微信渠道支付回调
	 *
	 * @param request
	 * @return
	 */
//	@Inner(false)
	@SneakyThrows
	@ResponseBody
	@PostMapping("/wx/wxRefundCallback")
	public String wxRefundCallback(HttpServletRequest request) {
		log.info("[微信退款回调] 请求信息:{}", JSON.toJSONString(request.getParameterMap()));
		String xmlMsg = HttpKit.readData(request);
		log.info("[微信退款回调] 解析请求信息为xml:{}", xmlMsg);
		Map<String, String> params = WxPayKit.xmlToMap(xmlMsg);
		return weChatRefundCallback.handle(params);
	}

	/**
	 * 合利宝支付回调
	 *
	 * @param response
	 * @return
	 */
//	@Inner(false)
	@SneakyThrows
	@ResponseBody
	@PostMapping("/helipay/callback")
	public String heliPayCallback(NotifyResponse response) {
		log.info("[合利宝支付回调] 请求信息:{}", JSON.toJSONString(response));
		return heliPayNotifyCallbackHandler.handle(HeliBeanUtils.convertBean(response));
	}

	/**
	 * 合利宝退款回调
	 *
	 * @param response
	 * @return
	 */
	@SneakyThrows
	@ResponseBody
	@PostMapping("/helipay/refundCallback")
	public String refundCallback(HeliRefundNotifyResponse response) {
		log.info("[合利宝退款回调] 请求信息:{}", JSON.toJSONString(response));
		return heliRefundNotifyCallbackHandler.handle(HeliBeanUtils.convertBean(response));
	}

	/**
	 * 合利宝代付回调
	 *
	 * @param response
	 * @return
	 */
	@SneakyThrows
	@ResponseBody
	@PostMapping("/helipay/transferCallback")
	public String transferCallback(HeliTransferNotifyResponse response) {
		log.info("[合利宝代付回调] 请求信息:{}", JSON.toJSONString(response));
		return heliTransferNotifyCallbackHandler.handle(HeliBeanUtils.convertBean(response));
	}



}
