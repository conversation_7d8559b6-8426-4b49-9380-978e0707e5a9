package com.eyuan.pay.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dog.common.core.constant.enums.ChannelTypeEnum;
import com.dog.common.core.util.R;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayChannel;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.request.PayChannelRequest;
import com.eyuan.pay.response.PayChannelDTO;
import com.eyuan.pay.response.PayChannelParamDTO;
import com.eyuan.pay.service.PayChannelService;
import com.eyuan.pay.service.PayChannelConfigService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 渠道
 *
 * <AUTHOR>
 * @date 2019-05-28 23:57:58
 */
@RestController
@AllArgsConstructor
@RequestMapping("/rest")
@Api(value = "restPaychannel", tags = "paychannel管理")
@Slf4j
public class RestPayChannelController {
	private final PayChannelConfigService payChannelConfigService;
	private final PayChannelService payChannelService;

	/**
	 * 查询
	 */
	@PostMapping("/front/payChannel/list")
	public R<List<PayChannelDTO>> payChannelList() {
		List<PayChannel> payChannelList = payChannelService.listAll();
		List<PayChannelDTO> collect = payChannelList.stream().map(payChannel -> {
			PayChannelDTO payChannelDTO = BeanUtil.copyProperties(payChannel, PayChannelDTO.class);
			if (StrUtil.isNotBlank(payChannel.getParam())) {
				PayChannelParamDTO payChannelParamDTO = JSONUtil.toBean(payChannel.getParam(), PayChannelParamDTO.class);
//				payChannelDTO.setPayChannelParamDTO(payChannelParamDTO);
			}
			return payChannelDTO;
		}).collect(Collectors.toList());
		return R.ok(collect);
	}

	@PostMapping("/front/payChannel/get")
	public R<PayChannelDTO> payChannelGet(@RequestBody @NotNull PayChannelRequest payChannelRequest) {
		LambdaQueryWrapper<PayChannel> wrapper = Wrappers.lambdaQuery();
		PayChannel payChannel = payChannelService.getOne(wrapper.eq(PayChannel::getTenantId, payChannelRequest.getTenantId()).eq(PayChannel::getChannelId, payChannelRequest.getChannelId()));
		if (payChannel != null) {
			PayChannelDTO payChannelDTO = BeanUtil.copyProperties(payChannel, PayChannelDTO.class);
			if (StrUtil.isNotBlank(payChannel.getParam())) {
				PayChannelParamDTO payChannelParamDTO = JSONUtil.toBean(payChannel.getParam(), PayChannelParamDTO.class);
//				payChannelDTO.setPayChannelParamDTO(payChannelParamDTO);
			}
			return R.ok(payChannelDTO);
		}
		return R.ok();
	}

	@GetMapping("/front/payChannel/getByChannelSource")
	public R<List<PayChannelDTO>> getByChannelSource(@RequestParam @NotNull String channelSource) {
		// 1. 根据渠道来源获取对应的支付工具枚举列表
		List<PayToolEnum> payToolEnums = getPayToolsByChannelSource(channelSource);
		if (payToolEnums.isEmpty()) {
			return R.ok(Collections.emptyList());
		}
		
		// 2. 查询并构建支付渠道配置
		List<PayChannelDTO> result = new ArrayList<>();
		for (PayToolEnum payTool : payToolEnums) {
			// 获取支付工具对应的渠道ID、支付类型和支付场景
			String channelId = payTool.payChanelValue();
			String payType = payTool.payTypeValue();
			String payScene = payTool.paySceneValue();
			
			try {
				// 使用getPaySceneConfig方法获取匹配的场景配置
				PaySceneDTO paySceneDTO = payChannelConfigService.getPaySceneConfig(channelId, payType, payScene);
				if (paySceneDTO != null) {
					// 转换为PayChannelDTO
					PayChannelDTO payChannelDTO = new PayChannelDTO();
					payChannelDTO.setChannelId(channelId);
					payChannelDTO.setPayTool(payTool.value());
					payChannelDTO.setAppId(paySceneDTO.getAppId());
					payChannelDTO.setPayType(paySceneDTO.getPayType());
					payChannelDTO.setPayScene(paySceneDTO.getPayScene());
					// 设置其他需要的属性
					
					result.add(payChannelDTO);
				}
			} catch (Exception e) {
				// 如果某个支付工具配置不存在或不可用，跳过并继续处理下一个
//				log.warn("获取支付渠道配置失败: channelId={}, payType={}, payScene={}, error={}",
//						channelId, payType, payScene, e.getMessage());
			}
		}
		
		return R.ok(result);
	}

	/**
	 * 根据渠道来源获取对应的支付工具枚举列表
	 * 
	 * @param channelSource 渠道来源
	 * @return 支付工具枚举列表
	 */
	private List<PayToolEnum> getPayToolsByChannelSource(String channelSource) {
		ChannelTypeEnum sourceEnum = ChannelTypeEnum.getByCode(channelSource);
		if (sourceEnum == null) {
			return Collections.emptyList();
		}
		
		// 根据渠道来源匹配对应的支付工具
		List<PayToolEnum> result = new ArrayList<>();
		switch (sourceEnum) {
			case PC:
				// PC端支持的支付工具
				result.add(PayToolEnum.ALIPAY_PAYMENT_CODE);
				result.add(PayToolEnum.WEIXIN_PAYMENT_CODE);
				break;
			case WX_APPLET:
				// 微信小程序支持的支付工具
				result.add(PayToolEnum.WEIXIN_MP);
				result.add(PayToolEnum.HELIPAY_WEIXIN_APPLET);
				break;
			case ALI_APPLET:
				// 支付宝小程序支持的支付工具
				result.add(PayToolEnum.ALIPAY_MP);
				break;
			case H5:
				// H5支持的支付工具
				result.add(PayToolEnum.ALIPAY_WAP);
				result.add(PayToolEnum.WEIXIN_WAP);
				result.add(PayToolEnum.HELIPAY_WEIXIN_WAP);
				break;
			case WXH5:
				// H5支持的支付工具
				result.add(PayToolEnum.WEIXIN_WAP);
				result.add(PayToolEnum.HELIPAY_WEIXIN_MP);
				break;
			default:
				break;
		}
		
		return result;
	}
}

