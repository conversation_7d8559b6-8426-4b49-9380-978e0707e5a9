package com.eyuan.pay.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.annotation.EnumBool;
import com.dog.common.core.util.R;
import com.dog.common.log.annotation.SysLog;
import com.eyuan.pay.entity.PayChannelConfig;
import com.eyuan.pay.service.PayChannelConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 支付渠道配置
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/payChannelConfig")
@Api(value = "payChannelConfig", tags = "支付渠道配置管理")
public class PayChannelConfigController {
    private final PayChannelConfigService payChannelConfigService;

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param payChannelConfig 支付渠道配置
     * @return 分页数据
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询", notes = "分页查询支付渠道配置")
    public R getPayChannelConfigPage(Page page, PayChannelConfig payChannelConfig) {
        return R.ok(payChannelConfigService.page(page, Wrappers.query(payChannelConfig)));
    }

    /**
     * 通过id查询支付渠道配置
     *
     * @param id id
     * @return 支付渠道配置
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "通过id查询", notes = "通过id查询支付渠道配置")
    public R getById(@PathVariable("id") Long id) {
        return R.ok(payChannelConfigService.getById(id));
    }

    /**
     * 保存或更新支付渠道配置
     *
     * @param payChannelConfig 支付渠道配置
     * @return 操作结果
     */
    @SysLog("保存支付渠道配置")
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新支付渠道配置", notes = "新增或修改支付渠道配置")
    public R saveOrUpdate(@RequestBody PayChannelConfig payChannelConfig) {
        // 保存或更新数据
        boolean result = payChannelConfigService.saveOrUpdateChannelConfig(payChannelConfig);
        return R.ok(result);
    }

    /**
     * 根据渠道ID查询支付渠道配置（路径参数）
     *
     * @param channelId 渠道ID
     * @return 支付渠道配置
     */
    @GetMapping("/channel/{channelId}")
    @ApiOperation(value = "根据渠道ID查询", notes = "根据渠道ID查询支付渠道配置")
    public R getByChannelId(@PathVariable("channelId") String channelId) {
        PayChannelConfig payChannelConfig = payChannelConfigService.findByChannelId(channelId);
        return R.ok(payChannelConfig);
    }

    /**
     * 启用支付渠道配置
     *
     * @param id 支付渠道配置ID
     * @return 操作结果
     */
    @SysLog("启用支付渠道配置")
    @PutMapping("/enable/{id}")
    @ApiOperation(value = "启用支付渠道配置", notes = "启用支付渠道配置")
    public R enable(@PathVariable Long id) {
        PayChannelConfig payChannelConfig = payChannelConfigService.getById(id);
        if (payChannelConfig != null) {
            // 设置状态为启用
            payChannelConfig.setState(EnumBool.TRUE.getValue());
            boolean result = payChannelConfigService.saveOrUpdateChannelConfig(payChannelConfig);
            return R.ok(result);
        }
        return R.failed("支付渠道配置不存在");
    }

    /**
     * 禁用支付渠道配置
     *
     * @param id 支付渠道配置ID
     * @return 操作结果
     */
    @SysLog("禁用支付渠道配置")
    @PutMapping("/disable/{id}")
    @ApiOperation(value = "禁用支付渠道配置", notes = "禁用支付渠道配置")
    public R disable(@PathVariable Long id) {
        PayChannelConfig payChannelConfig = payChannelConfigService.getById(id);
        if (payChannelConfig != null) {
            // 设置状态为禁用
            payChannelConfig.setState(EnumBool.FALSE.getValue());
            boolean result = payChannelConfigService.saveOrUpdateChannelConfig(payChannelConfig);
            return R.ok(result);
        }
        return R.failed("支付渠道配置不存在");
    }


    /**
     * 通过id删除支付渠道配置
     *
     * @param id id
     * @return 操作结果
     */
    @SysLog("删除支付渠道配置")
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除支付渠道配置", notes = "通过id删除支付渠道配置")
    public R removeById(@PathVariable Long id) {
        PayChannelConfig payChannelConfig = payChannelConfigService.getById(id);
        if (payChannelConfig != null) {
            return R.ok(payChannelConfigService.removeConfigById(id));
        }
        return R.failed("支付渠道配置不存在");
    }
}
