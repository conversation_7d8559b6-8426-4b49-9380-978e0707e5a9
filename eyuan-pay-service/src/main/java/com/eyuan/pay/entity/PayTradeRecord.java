package com.eyuan.pay.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 交易记录
 *
 * <AUTHOR>
 * @date 2019-05-28 23:57:23
 */
@Data
@TableName("pay_trade_record")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "异步通知记录")
public class PayTradeRecord extends Model<PayTradeRecord> {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Long id;

	/**
	 * 交易号
	 */
	private String tradeNo;

	/**
	 * 三方交易号
	 */
	private String thirdTradeNo;

	/**
	 * 交易类型
	 */
	private Integer tradeType;
	/**
	 * 请求报文
	 */
	private String request;
	/**
	 * 响应报文
	 */
	private String response;
	/**
	 * 业务订单号
	 */
	private String orderNo;
	/**
	 * 状态
	 */
	private String status;
	/**
	 * 删除标记
	 */
	@TableLogic
	private String deleted;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;
	/**
	 * 租户ID
	 */
	private String tenantId;

}
