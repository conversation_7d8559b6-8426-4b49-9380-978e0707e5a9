package com.eyuan.pay.client;

import com.eyuan.pay.config.WxNativePayProductInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 微信扫码支付调用类
 * @date 2020-11-02 11:24
 */
@Component
@AllArgsConstructor
@Slf4j
public class WxNativeProductClient {

	public WxNativePayProductInfo findProductInfo(String productId) {
		log.info("[微信扫码，回调业务处理, 查询业务系统商品信息] request, productId:{}", productId);
		WxNativePayProductInfo productInfo = new WxNativePayProductInfo();
		productInfo.setMoneyAmount(1L);
		productInfo.setProductName("1分测试商品");
		log.info("[微信扫码，回调业务处理, 查询业务系统商品信息] response, productid:{}, productInfo:{}", productId, productInfo);
		return null;
	}
}
