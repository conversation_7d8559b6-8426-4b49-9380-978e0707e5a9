package com.eyuan.pay.producer;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eyuan.pay.constant.PayMQConstants;
import com.eyuan.pay.request.OrderRefundNoticeRequest;
import com.eyuan.pay.utils.PayUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 退款结果通知业务模块
 * <AUTHOR>
 * @date 2023/12/12 11:22
 */
@Component
@Slf4j
public class RefundResultNoticeSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(OrderRefundNoticeRequest request) {
        log.info("退款结果通知业务模块参数：{}", JSONUtil.toJsonStr(request));
        request.setTenantId(PayUtil.getContextTenantId());
        String routingKey = "";
        switch (request.getAppPlatformType()) {
            case B2C:
                routingKey = PayMQConstants.MQ_REFUND_NOTICE_B2C_ROUTING_KEY;
                break;
            case HOME:
                routingKey = PayMQConstants.MQ_REFUND_NOTICE_HOTEL_ROUTING_KEY;
                break;
            case TICKET:
                routingKey = PayMQConstants.MQ_REFUND_NOTICE_TICKET_ROUTING_KEY;
                break;
            case TICKET_SERVICE:
                routingKey = PayMQConstants.MQ_REFUND_NOTICE_TICKET_SERVICE_ROUTING_KEY;
                break;
            default:
                log.error("[通知业务系统支付结果] 未知类型, request:{}", request);
        }
        if (StrUtil.isNotBlank(routingKey)) {
            // 不设置ConfirmCallback，使用全局配置的回调
            CorrelationData correlationData = new CorrelationData();
            correlationData.setId(UUID.fastUUID().toString());
            rabbitTemplate.convertAndSend(PayMQConstants.MQ_EXCHANGE,
                    routingKey,
                    request,
                    correlationData);
        }
    }

}
