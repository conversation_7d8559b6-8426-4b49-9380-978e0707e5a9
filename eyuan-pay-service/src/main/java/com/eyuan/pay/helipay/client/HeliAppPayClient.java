package com.eyuan.pay.helipay.client;

import com.dtflys.forest.annotation.*;
import com.eyuan.pay.helipay.interceptor.HeliAppPayInterceptor;
import com.eyuan.pay.helipay.response.AppPayAppletOrderResponse;
import com.eyuan.pay.helipay.response.AppPayPublicOrderResponse;
import com.eyuan.pay.helipay.response.AppPayRefundOrderResponse;
import com.eyuan.pay.helipay.response.AppPayWapCreateOrderResponse;

import java.util.Map;

/**
 * @Author: sunwh
 * @Date: 2025/4/7 16:23
 */
@BaseRequest(interceptor = HeliAppPayInterceptor.class)
public interface HeliAppPayClient {

    /**
     * 合利宝-小程序支付接口
     * @param request
     * @return
     */
    @Post(url = "{url}")
    AppPayAppletOrderResponse appPayAppletCreate(@Var("url") String url,@Body Map<String,String> request);

    /**
     * 合利宝-公众号/JS/服务窗支付接口
     * @param request
     * @return
     */
    @Post(url = "{url}")
    AppPayPublicOrderResponse appPayPublicCreate(@Var("url") String url, @Body Map<String,String> request);

    /**
     * 合利宝-wap支付接口
     * @param url
     * @param request
     * @return
     */
    @Post(url = "{url}")
    AppPayWapCreateOrderResponse wapCreateOrder(@Var("url") String url, @Body Map<String,String> request);

    /**
     * 合利宝-退款接口
     * @param request
     * @return
     */
    @Post(url = "{url}")
    AppPayRefundOrderResponse appPayAppletRefund(@Var("url") String url,@Body Map<String,String> request);
}
