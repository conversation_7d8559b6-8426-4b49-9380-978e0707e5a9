package com.eyuan.pay.helipay.request;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

/**
 * @create 2019-02-22 15:13
 */
@Setter
@Getter
@ToString
public class QueryCouponStockRequest {
    private String P1_bizType;
    private String customer_number;
    private String coupon_stock_id;
    private String op_user_id;
    private String device_info;
    private String nonce_str;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.MD5;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "customer_number",
            "coupon_stock_id", "op_user_id", "device_info", "nonce_str");
}
