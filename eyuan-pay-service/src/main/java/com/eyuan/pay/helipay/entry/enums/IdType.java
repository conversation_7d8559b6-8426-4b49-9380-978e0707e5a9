package com.eyuan.pay.helipay.entry.enums;

/**
 * 证件类型
 * <AUTHOR>
 *
 */
public enum IdType {
	/**
	 * 身份证
	 */
	IDCARD("身份证",0),
	/**
	 * 护照
	 */
	PASSPORT("护照",1),
	/**
	 * 士兵证
	 */
	SOLDIERSCERTIFICATE("士兵证",2),

	/**
	 * 军官证
	 */
	OFFICERSCERTIFICATE("军官证",4),
	/**
	 * 香港居民来往内地通行证
	 */
	GATXCERTIFICATE("香港居民来往内地通行证",5),
	/**
	 * 台湾同胞来往内地通行证
	 */
	TWNDCERTIFICATE("台湾同胞来往内地通行证",6),
	/**
	 * 澳门来往内地通行证
	 * 20191112新增
	 */
	MACAOCERTIFICATE("澳门来往内地通行证",18),

	/**
	 * 外国人居留证
	 */
	FOREIGNERCERTIFICATE("外国人居留证",8),

	/**
	 * 港澳居民居住证
	 */
	HKANDMACAORESIDENCEPERMIT("港澳居民证",15),

	/**
	 * 台湾居民居住证
	 */
	TAIWANRESIDENCEPERMIT("台湾居民证",16),

	/**
	 * 执行事务合伙人
	 */
	MANAGINGPARTNER("执行事务合伙人", 17),



	;

	IdType(String s, Integer i){
		this.desc = s;
		this.index = i;
	}

	private String desc;
	private Integer index;

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}
	

}
