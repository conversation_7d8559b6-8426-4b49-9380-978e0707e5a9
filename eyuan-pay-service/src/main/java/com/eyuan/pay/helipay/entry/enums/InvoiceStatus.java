package com.eyuan.pay.helipay.entry.enums;

/**
 * 商户开票状态
 *
 */
public enum InvoiceStatus {

	NEVERCHECK("待审核", 1),

	PASS("审核通过", 2),

	FAIL("审核拒绝", 3),


	;



	private String desc;
	private Integer index;

	private InvoiceStatus(String s, Integer i) {
		this.setDesc(s);
		this.setIndex(i);
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

}
