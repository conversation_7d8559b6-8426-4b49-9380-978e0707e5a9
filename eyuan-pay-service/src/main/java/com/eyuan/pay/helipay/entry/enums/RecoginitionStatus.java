package com.eyuan.pay.helipay.entry.enums;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2021-09-17 17:10
 */
public enum RecoginitionStatus {
    SAME("一致", 0),
    DIFF("不一致", 1),
    NONE("--", 2),
    ;

    private final String desc;
    private final Integer index;

    RecoginitionStatus(String desc, Integer index) {
        this.desc = desc;
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getIndex() {
        return index;
    }
}
