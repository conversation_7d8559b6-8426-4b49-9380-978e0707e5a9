package com.eyuan.pay.helipay.request;

import com.google.common.collect.ImmutableSet;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/8 14:07
 * @desc 支付分商户预下单通知请求
 */
@Data
public class AppProactivePrePayNotifyRequest {

    private String rt1_customerNumber;
    private String rt2_orderId;
    private String rt3_systemSerial;
    private String rt4_orderAmount;
    private String rt5_appPayType;
    private String rt6_payType;
    private String rt7_prePayReqBody;
    private String signatureType;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_customerNumber", "rt2_orderId",
            "rt3_systemSerial", "rt4_orderAmount", "rt5_appPayType", "rt6_payType", "rt7_prePayReqBody");

}
