package com.eyuan.pay.helipay.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 结算请求参数
 * @Author: sunwh
 * @Date: 2025/5/4 17:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HeliPaySettlementRequest implements java.io.Serializable{
    private static final long serialVersionUID = -1843447011675839823L;

    /**
     * 交易类型
     */
    @JSONField(name = "P1_bizType")
    private String P1_bizType;
    /**
     * 商户系统内部订单号，要求50字符以内，同一商户号下订单号唯一
     */
    @JSONField(name = "P2_orderId")
    private String P2_orderId;
    /**
     * 合利宝分配的商户
     */
    @JSONField(name = "P3_customerNumber")
    private String P3_customerNumber;
    /**
     * 金额
     */
    @JSONField(name = "P4_amount")
    private String P4_amount;
    @JSONField(name = "P5_summary")
    private String P5_summary;
    @JSONField(name = "P6_notifyUrl")
    private String P6_notifyUrl;
    @JSONField(name = "P7_transferSummary")
    private String P7_transferSummary;
    private String sign;
    /**
     * 签名类型(不参与签名)
     */
    private SignatureType signatureType = SignatureType.SM3WITHSM2;
    /**
     * sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)
     */
    private String encryptionKey;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId", "P3_customerNumber",
            "P4_amount", "P5_summary");
    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = Sets.newHashSet();

}
