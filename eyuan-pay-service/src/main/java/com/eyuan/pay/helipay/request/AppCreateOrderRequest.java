package com.eyuan.pay.helipay.request;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
public class AppCreateOrderRequest {


    private String P1_bizType;
    private String P2_orderId;
    private String P3_customerNumber;
    private String P4_payType;
    private String P5_orderAmount;
    private String P6_currency;
    private String P7_authcode;
    private String P8_appType;
    private String P9_notifyUrl;
    private String P10_successToUrl;
    private String P11_orderIp;
    private String P12_goodsName;
    private String P13_goodsDetail;
    private String P14_desc;
    /**特殊参数,排除签名*/
    private String P15_subMerchantId;
    private String P16_appId;
    private String P17_limitCreditPay;
    private String P18_goodsTag;
    private String P19_guid;
    private String P20_marketingRule;
    private String P21_identity;
    /**
     * @see extendParams
     */
    @Deprecated
    private String hbfqNum;
    private String deviceInfo;
    private String timeExpire;
    /**
     * 支付宝行业数据回流信息,排除签名
     *
     * @see extendParams
     */
    @Deprecated
    private String industryRefluxInfo;
    /**排除签名*/
    private String termInfo;
    /**预授权场景参数,排除签名*/
    private String openId;
    /**预授权场景参数,排除签名*/
    private String authConfirmMode;
    /**商户自定义门店编码,排除签名*/
    private String storeId;
    /**支付宝店铺编号*/
    private String alipayStoreId;

    /**分账参数开始(不签名)*/
    private String ruleJson;
    private String splitBillType;
    /**分账参数结束(不签名)*/

    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.MD5;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;

    private String sign;
    /**
     * 加密随机因子 排除签名
     */
    private String encryptRandNum;
    /**
     * 密文数据 排除签名
     */
    private String secretText;
    /**
     * 终端绑定号 排除签名
     */
    private String terminalSysBindNo;
    /**
     * 场景信息 排除签名
     */
    private String sceneInfo;
    /**
     * 服务商pid,不传以平台配置为准(仅银联二维码生效)
     */
    private String pid;
    /**
     * 业务扩展参数(排除签名)
     * 目前是扫码支付宝用:https://open.alipay.com/api
     * ********对{@link #hbfqNum}和{@link #industryRefluxInfo}和{@link #foodOrderType}进行整合,需要兼容存量商户传参
     */
    private String extendParams;
    /**
     * 商户传入业务信息，具体值要和支付宝约定
     */
    private String businessParams;
    /**
     * 传入学校名称和场景名称,支付宝参数
     */
    private String eduSubject;
    /**
     * 用户记账簿编号
     */
    private String boaAccountNo;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 储值订单类型
     * 枚举值:STOREDVALUE
     */
    private String storedValueOrderType;
    /**
     * 分账计费类型
     * 枚举值 STANDARD：默认标准 INDEPENDENT：独立计费
     */
    private String splitCalcFeeType;
    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType",
            "P2_orderId", "P3_customerNumber", "P4_payType", "P5_orderAmount",
            "P6_currency", "P7_authcode", "P8_appType", "P9_notifyUrl", "P10_successToUrl",
            "P11_orderIp", "P12_goodsName", "P13_goodsDetail", "P14_desc");
    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("ruleJson", "P20_marketingRule");
}
