package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

@Setter
@Getter
@ToString
public class AppPayPublicOrderResponse {


    private String rt1_bizType;
    private String rt2_retCode;
    private String rt3_retMsg;
    private String rt4_customerNumber;
    private String rt5_orderId;
    private String rt6_serialNumber;
    private String rt7_payType;
    private String rt8_appid;
    private String rt9_tokenId;
    private String rt10_payInfo;
    private String rt11_orderAmount;
    private String rt12_currency;
    private String rt13_channelRetCode;
    /**
     * 排除签名
     */
    private String subMerchantNo;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_bizType", "rt2_retCode", "rt4_customerNumber",
            "rt5_orderId", "rt6_serialNumber", "rt7_payType", "rt8_appid", "rt9_tokenId", "rt10_payInfo", "rt11_orderAmount",
            "rt12_currency");
}
