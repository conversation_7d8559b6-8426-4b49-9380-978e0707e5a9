package com.eyuan.pay.helipay.request;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

@Setter
@Getter
@ToString
public class AppPayCloseOrderRequest {


    private String P1_bizType;
    private String P2_orderId;
    private String P3_customerNumber;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.MD5;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId", "P3_customerNumber");
}
