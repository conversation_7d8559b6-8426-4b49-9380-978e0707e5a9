package com.eyuan.pay.helipay.entry.enums;

import java.util.HashMap;
import java.util.Map;

public enum SettleChangeType {

	NORMAL("银行卡",1),

	ELECTRONIC("银行电子账户",2);


	SettleChangeType(String s, Integer i){
		this.desc = s;
		this.index = i;
	}
	
	private String desc;
	private Integer index;
	
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	public Integer getIndex() {
		return index;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}

	public static Map<String, SettleChangeType> map = new HashMap<String, SettleChangeType>();

	static{
		SettleChangeType[] enums = SettleChangeType.values();
		for(SettleChangeType enumItem : enums){
			map.put(enumItem.name(), enumItem);
			map.put(enumItem.getDesc(), enumItem);
		}
	}

	public static SettleChangeType getSettleBankType(String key){
		for(String k : map.keySet()){
			if(k.contains(key)){
				return map.get(k);
			}
		}
		return null;
	}

}
