package com.eyuan.pay.helipay.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.dog.common.core.util.SpringContextHolder;
import com.eyuan.pay.config.HeliPayConfig;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.helipay.constant.HeliPayConstant;
import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.X509Certificate;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 合利宝签名工具类
 * @Author: sunwh
 * @Date: 2025/5/2 21:49
 */
@Slf4j
public class HeliSignatureUtils {
    /**
     * 商户SM2证书私钥字符串
     */
    public static PrivateKey signKey;
    /**
     * 合利宝SM2证书公钥对象
     */
    private static X509Certificate heliPayPublicCert;

    private static String SIGN_KEY_MD5;
    private static String DES_APP_PAY;
    
    private static boolean initialized = false;
    private static final Object lock = new Object();

    /**
     * 初始化签名工具类
     */
    private static void initialize() {
        if (!initialized) {
            synchronized (lock) {
                if (!initialized) {
                    try {
                        PayCommonProperties payCommonProperties = SpringContextHolder.getBean(PayCommonProperties.class);
                        HeliPayConfig heliPayConfig = payCommonProperties.getHeliPayConfig();

                        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
                            Security.addProvider(new BouncyCastleProvider());
                        }

                        try {
                            log.info("私钥路径：{}", heliPayConfig.getPrivateKeyPath());
                            signKey = KeyUtils.getPrivateKeyByPfx(heliPayConfig.getPrivateKeyPath(), heliPayConfig.getPrivateKeyPassword());
                        } catch (Exception e) {
                            log.error("Failed to load private key from path: {}", heliPayConfig.getPrivateKeyPath(), e);
                            throw new RuntimeException("获取SM2证书私钥字符串异常:" + e.getMessage(), e);
                        }

                        try {
                            heliPayPublicCert = HeliPayCertUtils.getX509Certificate(heliPayConfig.getPublicKeyPath());
                        } catch (Exception e) {
                            log.error("Failed to load public key from path: {}", heliPayConfig.getPublicKeyPath(), e);
                            throw new RuntimeException("加载合利宝SM2证书公钥异常异常:" + e.getMessage(), e);
                        }
                        initialized = true;
                    } catch (Exception e) {
                        log.error("初始化HeliSignatureUtils失败", e);
                        throw new RuntimeException("初始化HeliSignatureUtils失败: " + e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * 确保初始化完成
     */
    private static void ensureInitialized() {
        if (!initialized) {
            initialize();
        }
    }
    /**
     *
     * @param bean
     * @param needSignParams
     * @param needEncryptParams
     * @return
     */
    public static Map<String,String> convertRequestAndCreateSign(Object bean,Set<String> needSignParams, Set<String> needEncryptParams) {
        Map<String, String> dataMap = null;
        try {
            dataMap = HeliBeanUtils.convertBean(bean, new LinkedHashMap<>());
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        String sign = getSignAndEncryptedByReq(dataMap, needSignParams, needEncryptParams);
        dataMap.put("sign", sign);
        return dataMap;
    }
    /**
     * 固定参数签名
     */
    public static String getSignAndEncryptedByReq(Map<String, String> map, Set<String> needSignParams, Set<String> needEncryptParams) {
        ensureInitialized();
        if (needSignParams == null || needSignParams.isEmpty()) {
            throw new RuntimeException("needSignParams is required");
        }
        SecretKeySpec sm4Key = null;
        AtomicReference<Boolean> isSm4Encrypt = new AtomicReference<>(false);
        String signatureType = map.get("signatureType");
        SignatureType signatureTypeEnum;
        if (StringUtils.isBlank(signatureType) || StringUtils.equals(SignatureType.MD5.name(), signatureType)) {
            signatureTypeEnum = SignatureType.MD5;
        } else if (StringUtils.equals(SignatureType.SM3WITHSM2.name(), signatureType)) {
            sm4Key = SM4Utils.generateSm4Key();
            signatureTypeEnum = SignatureType.SM3WITHSM2;
        } else {
            throw new RuntimeException("signatureType is illegal");
        }
        if (needEncryptParams == null) {
            needEncryptParams = Sets.newHashSet();
        }
        if (signatureTypeEnum == SignatureType.MD5) {
            needEncryptParams.forEach(key -> {
                des3Encrypt(map.get(key), key, map);
            });
        } else {
            SecretKeySpec finalSm4Key = sm4Key;
            needEncryptParams.forEach(key -> {
                boolean b = sm4Encrypt(map.get(key), key, map, finalSm4Key);
                if (b) {
                    isSm4Encrypt.set(true);
                }
            });

            //请求参数中有需要加密的字段，使用合利宝的公钥的对SM4密钥KEY进行加密处理
            if (isSm4Encrypt.get()) {
                String encryptionKey = SM2Utils.encryptBase64(sm4Key.getEncoded(), heliPayPublicCert);
                map.put("encryptionKey", encryptionKey);
            }
        }
        StringBuffer sb = doSignStrAppend(map, needSignParams);
        String sign;
        if (signatureTypeEnum == SignatureType.MD5) {
            //使用商户的md5密钥进行摘要签名
            sb.append(HeliPayConstant.SPLIT).append(SIGN_KEY_MD5);
            sign = Disguiser.disguiseMD5(sb.toString().trim());
        } else {
            //使用商户的私钥进行签名
            sign = SM2SignUtils.sign(signKey, sb.toString());
        }
        map.put("sign", sign);
        log.info("sign 原串(待签名串):{}", sb);
        log.info("{}签名结果：{}", signatureType, sign);
        return sign;
    }


    /**
     * 固定参数验签
     */
    public static boolean verifySignByRes(Map<String, String> map, Set<String> needSignParams) {
        ensureInitialized();
        final String resSign = map.get("sign");
        if (StringUtils.isBlank(resSign)) {
            throw new RuntimeException("sign is null");
        }
        String signatureType = map.get("signatureType");
        SignatureType signatureTypeEnum;
        if (StringUtils.isBlank(signatureType) || StringUtils.equals(SignatureType.MD5.name(), signatureType)) {
            signatureTypeEnum = SignatureType.MD5;
        } else if (StringUtils.equals(SignatureType.SM3WITHSM2.name(), signatureType)) {
            signatureTypeEnum = SignatureType.SM3WITHSM2;
        } else {
            throw new RuntimeException("signatureType is illegal");
        }
        StringBuffer sb = doSignStrAppend(map, needSignParams);
        boolean verify;
        if (signatureTypeEnum == SignatureType.MD5) {
            //使用商户的md5密钥进行摘要签名
            sb.append(HeliPayConstant.SPLIT).append(SIGN_KEY_MD5);
            String sign = Disguiser.disguiseMD5(sb.toString().trim());
            verify = StringUtils.equalsIgnoreCase(resSign, sign);
        } else {
            //使用合利宝公钥进行验签
            verify = SM2SignUtils.verify(sb.toString(), resSign, heliPayPublicCert,null);
        }
        log.info("请求签名：{}", resSign);
        log.info("sign 原串(待签名串):{}", sb);
        log.info("验签{}", verify ? "成功" : "失败");
        return verify;
    }



    private static StringBuffer doSignStrAppend(Map<String, String> map, Set<String> needSignParams) {
        StringBuffer sb = new StringBuffer();
        for (String needSignParam : needSignParams) {
            String value = map.get(needSignParam);
            value = (StrUtil.isBlank(value) ? "" : value);
            sb.append(HeliPayConstant.SPLIT).append(value);
        }
        return sb;
    }

    /**
     * 3des对敏感信息加密
     *
     * @param value
     * @param key
     * @param paramMap
     * @return
     */
    private static String des3Encrypt(String value, String key, Map<String, String> paramMap) {
        if (StringUtils.isNotBlank(value)) {
            log.info("3des加密前明文:{}", value);
            try {
                value = DesUtils.encode(DES_APP_PAY, value.trim());
            } catch (Exception e) {
                log.error("3des error:", e);
            }
            log.info("3des加密后result:{}", value);
            paramMap.put(key, value);
        }
        return value;
    }

    private static String des3Decrypt(String value, String key, Map<String, String> paramMap) {
        if (StringUtils.isNotBlank(value)) {
            log.info("3des密文:{}", value);
            try {
                value = DesUtils.decode(DES_APP_PAY, value.trim());
            } catch (Exception e) {
                log.error("3des error:", e);
            }
            log.info("3des解密后result:{}", value);
            paramMap.put(key, value);
        }
        return value;
    }

    /**
     * sm4对敏感信息加密
     *
     * @param value
     * @param key
     * @param paramMap
     * @param sm4Key
     * @return
     */
    private static boolean sm4Encrypt(String value, String key, Map<String, String> paramMap, SecretKeySpec sm4Key) {
        if (StringUtils.isNotBlank(value)) {
            Preconditions.checkArgument(ObjectUtil.isNotNull(sm4Key), "sm4Key Can't be empty");
            log.info("sm4加密前明文:{}", value);
            try {
                value = SM4Utils.encryptBase64(sm4Key, value.trim());
            } catch (Exception e) {
                log.error("sm4 error:", e);
            }
            log.info("sm4加密后result:{}", value);
            paramMap.put(key, value);
            return true;
        }
        return false;
    }

    private static boolean sm4Decrypt(String value, String key, Map<String, String> paramMap, SecretKeySpec sm4Key) {
        if (StringUtils.isNotBlank(value)) {
            log.info("sm4密文:{}", value);
            try {
                value = new String(Base64.encode(SM4Utils.decrypt(sm4Key,value.getBytes(StandardCharsets.UTF_8))));
            } catch (Exception e) {
                log.error("sm4 error:", e);
            }
            log.info("sm4解密后result:{}", value);
            paramMap.put(key, value);
            return true;
        }
        return false;
    }

}
