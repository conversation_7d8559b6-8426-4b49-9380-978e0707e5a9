package com.eyuan.pay.helipay.util;

import cn.hutool.core.io.IoUtil;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Objects;

/**
 * @description:
 * @author: zhenghf
 * @since:
 */
public abstract class CertUtils {

    private CertUtils() {}

    public static X509Certificate getX509CertificateUseBCP(InputStream is) {
        Objects.requireNonNull(is);
        return getX509CertificateUseBCP(IoUtil.read(is, StandardCharsets.UTF_8));
    }


    public static X509Certificate getX509CertificateUseBCP(String pem) {
        Objects.requireNonNull(pem, "pem required");

        if (!pem.contains("BEGIN CERTIFICATE")) {
            pem = "-----BEGIN CERTIFICATE-----\n" + pem +
                    "\n-----END CERTIFICATE-----";
        }

        try {
            CertificateFactory factory = CertificateFactory.getInstance("X.509", InternalBCHelper.getProvider());
            InputStream in = new ByteArrayInputStream(pem.getBytes());
            Certificate certificate = factory.generateCertificate(in);
            return (X509Certificate) certificate;
        } catch (CertificateException e) {
            ExceptionUtils.rethrow(e);
            return null;
        }
    }

}