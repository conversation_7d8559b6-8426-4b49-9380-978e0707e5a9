package com.eyuan.pay.helipay.request;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
public class AppPaySdkOrderRequest {

    private String P1_bizType;
    private String P2_customerNumber;
    private String P3_orderId;
    private String P4_goodsName;
    private String P5_orderAmount;
    private String P6_currency;
    private String P7_orderIp;
    private String P8_notifyUrl;
    private String P9_isRaw;
    /**
     * 排除签名
     * 支付宝非原生态模式: 值为 NO_SDK,APP不需集成SDK; 值为SDK,APP需要集成SDK; 不传默认是 NO_SDK
     */
    private String nonRawMode;
    private String P10_appPayType;
    private String P11_limitCreditPay;
    private String P12_deviceInfo;
    private String P13_appid;
    private String P14_desc;
    //不签名
    private String P15_subMerchantId;
    //不签名
    private String ruleJson;
    //不签名
    private String splitBillType;

    /**
     * 终端绑定号 排除签名
     */
    private String terminalSysBindNo;

    /**
     * 场景信息 排除签名
     */
    private String sceneInfo;
    /**
     * 成功跳转url,支付宝非原生时此参数才有效
     * 不参与签名
     */
    private String successToUrl;

    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.MD5;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    /**
     * 用户记账簿编号
     */
    private String boaAccountNo;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 储值订单类型
     * 枚举值:STOREDVALUE
     */
    private String storedValueOrderType;
    /**
     * 分账计费类型
     * 枚举值 STANDARD：默认标准 INDEPENDENT：独立计费
     */
    private String splitCalcFeeType;
    private String sign;

    public void setNonRawMode(String nonRawMode) {
        if (!StringUtils.equals("ALIPAY", getP10_appPayType())
                && !StringUtils.equals("0", getP9_isRaw())) {
            nonRawMode = null;
        }
        this.nonRawMode = nonRawMode;
    }

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_customerNumber", "P3_orderId",
            "P4_goodsName", "P5_orderAmount", "P6_currency", "P7_orderIp", "P8_notifyUrl", "P9_isRaw", "P10_appPayType",
            "P11_limitCreditPay", "P12_deviceInfo", "P13_appid", "P14_desc");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("ruleJson");
}
