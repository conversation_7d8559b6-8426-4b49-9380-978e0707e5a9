package com.eyuan.pay.helipay.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
public class AppPayRefundOrderRequest {


    @JSONField(name = "P1_bizType")
    private String P1_bizType;
    @JSONField(name = "P2_orderId")
    private String P2_orderId;
    @JSONField(name = "P3_customerNumber")
    private String P3_customerNumber;
    @JSONField(name = "P4_refundOrderId")
    private String P4_refundOrderId;
    @JSONField(name = "P5_amount")
    private String P5_amount;
    @JSONField(name = "P6_callbackUrl")
    private String P6_callbackUrl;
    @JSONField(name = "P7_desc")
    private String P7_desc;
    @JSONField(name = "P8_orderSerialNumber")
    private String P8_orderSerialNumber;
    /**排除签名*/
    private String acqAddnData;
    /**排除签名*/
    private String ruleJson;
    /**
     * 退款待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.SM3WITHSM2;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId",
            "P3_customerNumber", "P4_refundOrderId", "P5_amount", "P6_callbackUrl");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("ruleJson");
}
