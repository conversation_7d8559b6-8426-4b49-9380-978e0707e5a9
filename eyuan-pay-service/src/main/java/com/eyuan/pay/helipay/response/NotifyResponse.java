package com.eyuan.pay.helipay.response;


import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
public class NotifyResponse {

    private String rt1_customerNumber;
    private String rt2_orderId;
    private String rt3_systemSerial;
    private String rt4_status;
    private String rt5_orderAmount;
    private String rt6_currency;
    private String rt7_timestamp;
    private String rt8_desc;
    /**
     * 上游返回 :现金支付金额, (订单总金额-现金券金额=现金支付金额)
     */
    private String rt13_onlineCardType;
    /**
     * 后收手续费(不参与签名)
     */
    private BigDecimal offlineFee;
    /**
     * 实时收手续费(不参与签名)
     */
    private BigDecimal receiverFee;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 可分账金额
     */
    private BigDecimal splittableAmount;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_customerNumber", "rt2_orderId",
            "rt3_systemSerial", "rt4_status", "rt5_orderAmount", "rt6_currency", "rt7_timestamp", "rt8_desc");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("rt21_marketingRule");
}
