package com.eyuan.pay.helipay.util;


import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.spec.ECParameterSpec;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.X509Certificate;
import java.util.Objects;

/**
 * @description:
 * @author: zhenghf
 * @since:
 */
public class SM2SignUtils {

    public final static String CURVE_NAME = "sm2p256v1";
    private final static byte[] USER_ID = "1234567812345678".getBytes();

    /**
     * 获取 SM2 的曲线参数
     *
     * @return SM2 的曲线参数
     */
    private static ECDomainParameters getECDomainParameters() {
        final ECParameterSpec spec = ECNamedCurveTable.getParameterSpec(CURVE_NAME);
        return new ECDomainParameters(spec.getCurve(), spec.getG(), spec.getN(), spec.getH(), spec.getSeed());
    }

    /**
     * 将私钥字节数组编码为私钥对象
     *
     * @param value 私钥字节数组，32 字节
     * @return 私钥对象
     */
    private static ECPrivateKeyParameters encodePrivateKey(byte[] value) {
        final BigInteger d = new BigInteger(1, value);
        return new ECPrivateKeyParameters(d, getECDomainParameters());
    }


    /**
     * 加签
     * @param privateKey 私钥
     * @param data 签名原文
     * @return 签名串
     */
    public static String sign(PrivateKey privateKey, String data) {
        return sign(privateKey, data.getBytes(StandardCharsets.UTF_8), null);
    }



    /**
     * 加签
     * @param privateKeyPath  私钥路径
     * @param password  私钥密码
     * @param data 签名原文
     * @return 签名串
     */
    public static String sign(String privateKeyPath, String password, byte[] data, byte[] id) throws IOException {
        PrivateKey privateKey = KeyUtils.getPrivateKeyByPfx(privateKeyPath, password);
        return sign(privateKey, data, id);
    }


    /**
     * 加签
     * @param privateKey 私钥
     * @param data 签名原文
     * @return 签名串
     */
    public static String sign(PrivateKey privateKey, String data, byte[] id) {
        return sign(privateKey, data.getBytes(StandardCharsets.UTF_8), id);
    }


    /**
     * 加签
     * @param privateKey 私钥
     * @param data 签名原文
     * @return 签名串
     */
    public static String sign(PrivateKey privateKey, byte[] data, byte[] id) {
        SM2 sm2 = new SM2(privateKey, null);
        return new String(Base64.encode(sm2.sign(data, id)));
    }

    /**
     * 验签
     * @param plainText 原文
     * @param signText base64 串
     * @param cerPem 公钥证书 pem 串
     */
    public static boolean verifyUsingPemStr(String plainText, String signText, String cerPem) {
        return verifyUsingPemStr(plainText, signText, cerPem, null);
    }

    /**
     * 验签
     * @param plainText 原文
     * @param signText base64 串
     * @param cerFilePath 公钥证书文件路径
     * @param id
     * @throws IOException
     */
    public static boolean verify(String plainText, String signText, String cerFilePath, byte[] id) throws IOException {
        Objects.requireNonNull(cerFilePath);
        X509Certificate certificate;
        try (InputStream inputStream = Files.newInputStream(Paths.get(cerFilePath))) {
            certificate = CertUtils.getX509CertificateUseBCP(inputStream);
        }
        return verify(plainText, signText, certificate, id);
    }


    /**
     * 验签
     * @param plainText 原文
     * @param signText base64 串
     * @param cerPem 公钥证书 pem 串
     * @param id
     */
    public static boolean verifyUsingPemStr(String plainText, String signText, String cerPem, byte[] id) {
        X509Certificate certificate = CertUtils.getX509CertificateUseBCP(cerPem);
        return verify(plainText, signText, certificate, id);
    }

    public static boolean verify(String plainText, String signText, X509Certificate certificate, byte[] id) {
        Objects.requireNonNull(plainText, "plainText required");
        Objects.requireNonNull(signText, "signText required");
        return verify(plainText.getBytes(StandardCharsets.UTF_8), Base64.decode(signText.getBytes()), certificate, id);
    }


    private static boolean verify(byte[] plainData, byte[] signature, X509Certificate certificate, byte[] id) {
        return verify(plainData, signature, certificate.getPublicKey(), id);
    }

    public static boolean verify(byte[] plainData, byte[] signature, PublicKey publicKey, byte[] id) {
        SM2 sm2 = new SM2(null, publicKey);
        return sm2.verify(plainData, signature, id);
    }


}
