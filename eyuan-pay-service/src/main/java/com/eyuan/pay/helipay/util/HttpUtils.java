package com.eyuan.pay.helipay.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: sunwh
 * @Date: 2025/6/3 16:18
 */
public class HttpUtils {

    /**
     * 执行调用
     * @param url
     * @param map
     * @return
     */
    public static String doExecute(String url, Map<String, String> map) {
        Map<String, Object> params = new HashMap<>(map);
        HttpResponse response = HttpRequest.post(url)
                .form(params) // 设置表单数据
                .timeout(5000) // 设置超时时间，单位毫秒
                .execute();
        int status = response.getStatus();
        return response.body();
    }
}
