package com.eyuan.pay.helipay.util;

import com.alibaba.spring.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 */
@Slf4j
public class HeliBeanUtils extends BeanUtils {

    public static Map<String,String> convertBean(Object bean) throws IllegalAccessException {
        Map<String ,String> retMap = new LinkedHashMap<>();
        Class clazz = bean.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            f.setAccessible(true);
        }
        for (Field f : fields) {
            String key = f.toString().substring(f.toString().lastIndexOf(".") + 1);
            if (StringUtils.equalsIgnoreCase("NEED_SIGN_PARAMS", key)
                    || StringUtils.equalsIgnoreCase("NEED_ENCRYPT_OR_DECRYPT_PARAMS", key)) {
                continue;
            }
            Object value = f.get(bean);
            if (value == null) {
                value = "";
            }
            retMap.put(key, value.toString());
        }
        return retMap;
    }

    public static Map<String,String> convertBean(Object bean,LinkedHashMap<String,String> retMap) throws IllegalAccessException {
        Class clazz = bean.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            f.setAccessible(true);
        }
        for (Field f : fields) {
            String key = f.toString().substring(f.toString().lastIndexOf(".") + 1);
            if (StringUtils.equalsIgnoreCase("NEED_SIGN_PARAMS", key)
                    || StringUtils.equalsIgnoreCase("NEED_ENCRYPT_OR_DECRYPT_PARAMS", key)) {
                continue;
            }
            Object value = f.get(bean);
            if (value == null) {
                value = "";
            }
            retMap.put(key, value.toString());
        }
        return retMap;
    }
}
