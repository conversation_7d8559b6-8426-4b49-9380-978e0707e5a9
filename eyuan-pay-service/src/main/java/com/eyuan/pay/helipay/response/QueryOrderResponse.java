package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
public class QueryOrderResponse {

    private String rt1_bizType;
    private String rt2_retCode;
    private String rt3_retMsg;
    private String rt4_customerNumber;
    private String rt5_orderId;
    private String rt6_serialNumber;
    private String rt7_orderStatus;
    private String rt8_orderAmount;
    private String rt9_currency;
    private String rt10_desc;
    private String rt11_openId;
    private String rt12_channelOrderNum;
    private String rt13_orderCompleteDate;
    private String rt14_cashFee;
    private String rt15_couponFee;
    private String rt16_onlineCardType;
    private String rt17_fundBillList;
    private String rt18_outTransactionOrderId;
    private String rt19_bankType;
    private String rt20_subOpenId;
    /**
     * 排除签名
     */
    private String subMerchantNo;
    /**
     * 后收手续费(不参与签名)
     */
    private BigDecimal offlineFee;
    /**
     * 实时收手续费(不参与签名)
     */
    private BigDecimal receiverFee;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 可分账金额
     */
    private BigDecimal splittableAmount;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    private String sign;
    /**
     * 需要加签的属性参数
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_bizType", "rt2_retCode", "rt4_customerNumber",
            "rt5_orderId", "rt6_serialNumber", "rt7_orderStatus", "rt8_orderAmount", "rt9_currency");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("rt22_marketingRule");
}
