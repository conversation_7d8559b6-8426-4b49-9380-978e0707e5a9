package com.eyuan.pay.helipay.request;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Data;

import java.util.Set;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-02-28 11:17
 * @desc
 */
@Data
public class AppPreAuthorizationRequest {
    /**
     *
     */
    private String P1_bizType;
    private String P2_orderId;
    private String P3_customerNumber;
    private String P4_parentCustomerNumber;
    private String P5_preAuthReqBody;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.MD5;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId", "P3_customerNumber",
            "P4_parentCustomerNumber", "P5_preAuthReqBody");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of();
}
