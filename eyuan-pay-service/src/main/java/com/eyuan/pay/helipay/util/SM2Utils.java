package com.eyuan.pay.helipay.util;

import cn.hutool.crypto.asymmetric.SM2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Security;
import java.security.Signature;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Objects;

/**
 * 国密SM2算法工具类
 *
 * @description:
 * @since:
 */
@Slf4j
public class SM2Utils {

    private SM2Utils() {
    }

//    public static String sign(String plainText, String privateKeyStr) {
//        SM2 sm2 = new SM2(privateKeyStr, null);
//        byte[] data = plainText.getBytes(StandardCharsets.UTF_8);
//        return Base64.getEncoder().encodeToString(sm2.sign(data, null));
//    }

    public static boolean verify(String plainText, String signText, X509Certificate certificate) {
        Objects.requireNonNull(plainText, "plainText required");
        Objects.requireNonNull(signText, "signText required");
        return verify(plainText.getBytes(StandardCharsets.UTF_8), Base64.getDecoder().decode(signText), certificate);
    }

    private static boolean verify(byte[] plainData, byte[] signature, X509Certificate certificate) {
        try {
            Signature verifySign = Signature.getInstance(certificate.getSigAlgName());
            verifySign.initVerify(certificate);
            verifySign.update(plainData);
            return verifySign.verify(signature);
        } catch (Exception e) {
            log.error("verify", e);
            ExceptionUtils.rethrow(e);
            return false;
        }
    }

    public static String encryptToBase64(String plainText, X509Certificate cert) {
        byte[] cipherData = encrypt(plainText.getBytes(StandardCharsets.UTF_8), cert);
        return Base64.getEncoder().encodeToString(cipherData);
    }
    public static String encryptBase64(byte[] message, X509Certificate cert) {
        byte[] bytes = encrypt(message, cert);
        return new String(com.eyuan.pay.helipay.util.Base64.encode(bytes));
    }

    private static byte[] encrypt(byte[] message, X509Certificate cert) {
        SM2 sm2 = new SM2(null, cert.getPublicKey());
        sm2.setMode(SM2Engine.Mode.C1C3C2);
        return sm2.encrypt(message);
    }

    public static String decryptBase64Message(String cipherText, String privateKeyStr) {
        byte[] base64Data = Base64.getDecoder().decode(cipherText);
        byte[] bytes = decrypt(base64Data, privateKeyStr);
        return StringUtils.toEncodedString(bytes, StandardCharsets.UTF_8);
    }

    private static byte[] decrypt(byte[] message, String privateKeyStr) {
        SM2 sm2 = new SM2(privateKeyStr, null);
        sm2.setMode(SM2Engine.Mode.C1C3C2);
        return sm2.decrypt(message);
    }

    public static void main(String[] args) {
        String privateKeyStr = "ME0CAQAwEwYHKoZIzj0CAQYIKoEcz1UBgi0EMzAxAgEBBCAUjaMdaFPFhPoYpc81IA+Z0RE5r6UwGBIB7POOnXnLx6AKBggqgRzPVQGCLQ==";
        // 如果是Base64编码的，尝试解码
        if (privateKeyStr.matches("^[A-Za-z0-9+/]+={0,2}$")) {
            try {
                byte[] decoded = Base64.getDecoder().decode(privateKeyStr);
                // 这里可以添加更多的私钥格式检查逻辑
                // 重新编码为Base64
                privateKeyStr = Base64.getEncoder().encodeToString(decoded);
            } catch (Exception e) {
                log.warn("私钥Base64解码失败，使用原始私钥: {}", e.getMessage());
            }
        }
        String signStr = "&AppPayApplet&2025052413211495510740&C1808903708&APPLET&wxa6b72a42c476652f&&&ons_S5D8uM_WNenZPnPiNHenTT8M&0.01&CNY&WXPAY&http://***********:5010/rest/notify/helipay/callback&&*************&【龙门秘境】戴妃皇菊&&&";
        String target = sign(signStr, privateKeyStr);
        String str = "MEYCIQCynBQmDFEWoYqzZJkY46Ht85WodnQZhDG+l/8W7fDbQQIhAOQhXfnj0/ZiNu5ATxjFccD86tyDUQDKOa4VfvzIYZCb";
        System.out.println(str.replaceAll("\\s+", ""));
    }

    public static String sign(String plainText, String privateKeyStr) {
        try {
            // 注册BouncyCastle提供者
            Security.addProvider(new BouncyCastleProvider());

            // 解析私钥
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyStr);
            KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
            PrivateKey privateKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(privateKeyBytes));

            // 创建签名对象
            Signature signature = Signature.getInstance("SM3withSM2", "BC");
            signature.initSign(privateKey);
            signature.update(plainText.getBytes(StandardCharsets.UTF_8));

            // 计算签名
            byte[] signatureBytes = signature.sign();

            // 返回Base64编码的签名
            return Base64.getEncoder().encodeToString(signatureBytes).replaceAll("\\s+", "");
        } catch (Exception e) {
            log.error("SM2签名失败: {}", e.getMessage(), e);

            // 临时解决方案：如果SM2签名失败，尝试使用MD5签名
            log.warn("SM2签名失败，建议临时切换到MD5签名方式");

            throw new RuntimeException("SM2签名失败: " + e.getMessage(), e);
        }
    }

}