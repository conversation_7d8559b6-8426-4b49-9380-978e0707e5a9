package com.eyuan.pay.helipay.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.ASN1Encodable;
import org.bouncycastle.asn1.ASN1OctetString;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.pkcs.PKCSObjectIdentifiers;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.modes.CBCBlockCipher;
import org.bouncycastle.crypto.paddings.PKCS7Padding;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Enumeration;
import java.util.Objects;

/**
 * 合利宝证书工具类
 */
@Slf4j
public abstract class HeliPayCertUtils {

    private HeliPayCertUtils() {
    }

    public static PublicKey getPublicKey(String certFilePath) throws Exception {
        return getX509Certificate(certFilePath).getPublicKey();
    }

    /**
     * 获取证书
     *
     * @param certFilePath
     * @return
     * @throws CertificateException
     * @throws NoSuchProviderException
     * @throws IOException
     */
    public static X509Certificate getX509Certificate(String certFilePath) throws Exception {
        try {
            CertificateFactory cf = CertificateFactory.getInstance("X.509", BouncyCastleProvider.PROVIDER_NAME);
            byte[] readAllBytes = Files.readAllBytes(Paths.get(certFilePath));
            String fileContent = new String(readAllBytes);
            if ("-----BEGIN CERTIFICATE-----".indexOf(fileContent) < 0) {
                fileContent = "-----BEGIN CERTIFICATE-----\n" + fileContent +
                        "\n-----END CERTIFICATE-----";
            }
            InputStream is = new ByteArrayInputStream(fileContent.getBytes());
            return (X509Certificate) cf.generateCertificate(is);
        } catch (IOException | NoSuchProviderException | CertificateException e) {
            log.error("getX509Certificate", e);
            throw new CertificateException(e.getMessage(), e);
        }
    }

    /**
     * 获取私钥字符串
     *
     * @param sm2FilePath
     * @param sm2FilePwd
     * @return
     * @throws IOException
     */
    public static String getPrivateKeyStr(String sm2FilePath, String sm2FilePwd) throws Exception {
        Objects.requireNonNull(sm2FilePath, "sm2FilePath required");
        Objects.requireNonNull(sm2FilePwd, "sm2FilePwd required");
        //新证书是pfx格式
        if (sm2FilePath.endsWith(".pfx")) {
            return getPrivateKeyForPfx(sm2FilePath, sm2FilePwd);
        } else {
            return getPrivateKeyForSm2(sm2FilePath, sm2FilePwd);
        }
    }

    @Deprecated
    private static String getPrivateKeyForSm2(String sm2FilePath, String sm2FilePwd) throws Exception {
        byte[] data = Files.readAllBytes(Paths.get(sm2FilePath));
        try {
            PKCS12_SM2 p12 = new PKCS12_SM2(data);
            return p12.getPrivateKey(sm2FilePwd);
        } catch (Exception e) {
            log.error("getPrivateKeyStr", e);
            throw new IllegalArgumentException(e.getMessage(), e);
        }
    }

    private static String getPrivateKeyForPfx(String pfxFilePath, String pfxFilePwd) throws Exception {
        try (FileInputStream fis = new FileInputStream(pfxFilePath);) {
            KeyStore ks = KeyStore.getInstance("PKCS12", BouncyCastleProvider.PROVIDER_NAME);

            // If the keystore password is empty(""), then we have to set
            // to null, otherwise it won't work!!!
            char[] nPassword = null;
            if ((pfxFilePwd == null) || pfxFilePwd.trim().equals("")) {
                nPassword = null;
            } else {
                nPassword = pfxFilePwd.toCharArray();
            }
            ks.load(fis, nPassword);
            fis.close();
            Enumeration enumas = ks.aliases();
            String keyAlias = null;
            if (enumas.hasMoreElements())// we are readin just one certificate.
            {
                keyAlias = (String) enumas.nextElement();
            }
            PrivateKey prikey = (PrivateKey) ks.getKey(keyAlias, nPassword);
            return org.apache.commons.codec.binary.Base64.encodeBase64String(prikey.getEncoded());
        } catch (Exception e) {
            log.error("getPrivateKeyStr", e);
            throw new IllegalArgumentException(e.getMessage(), e);
        }
    }

    @Deprecated
    private static class PKCS12_SM2 implements ASN1Encodable, PKCSObjectIdentifiers {

        private ASN1Sequence privateInfo = null;

        public PKCS12_SM2() {
        }

        public PKCS12_SM2(byte[] data) throws Exception {
            load(data);
        }

        public void load(byte[] data) throws Exception {
            ASN1Sequence seq = getDERSequenceFrom(data);
            parseSM2(seq);
        }

        public void parseSM2(ASN1Sequence seq) throws Exception {
            parseSM2Certs((ASN1Sequence) seq.getObjectAt(1), (ASN1Sequence) seq.getObjectAt(2));
        }

        private void parseSM2Certs(ASN1Sequence privateInfo, ASN1Sequence publicInfo) {
            this.privateInfo = privateInfo;
        }

        public static ASN1Sequence getDERSequenceFrom(byte[] encoding) throws Exception {
            if (isDERSequence(encoding)) {
                return ASN1Sequence.getInstance(encoding);
            }
            if (isBERSequence(encoding)) {
                return ASN1Sequence.getInstance(encoding);
            }
            byte[] data;
            try {
                data = Base64.decode(encoding);
            } catch (Exception e) {
                throw new Exception("encoding required base64 encoding", e);
            }
            return ASN1Sequence.getInstance(data);
        }

        public static boolean isBERSequence(byte[] encoding) throws Exception {
            if (encoding == null) {
                throw new Exception("encoding should not be null");
            }
            if (encoding.length < 4) {
                throw new Exception("encoding length less than 4");
            }
            if (encoding[0] != 48) {
                return false;
            }
            int offset = 1;
            int length = encoding[(offset++)] & 0xFF;
            if (length != 128) {
                return false;
            }
            return (encoding[(encoding.length - 1)] == 0) && (encoding[(encoding.length - 2)] == 0);
        }

        public static boolean isDERSequence(byte[] encoding) throws Exception {
            if (encoding == null) {
                throw new Exception("encoding should not be null");
            }
            if (encoding.length < 2) {
                throw new Exception("encoding length less than 4");
            }
            if (encoding[0] != 48) {
                return false;
            }
            int offset = 1;
            int length = encoding[(offset++)] & 0xFF;
            if (length == 128) {
                return false;
            }
            if (length > 127) {
                int dLength = length & 0x7F;
                if (dLength > 4) {
                    return false;
                }
                length = 0;
                int next = 0;
                for (int i = 0; i < dLength; i++) {
                    next = encoding[(offset++)] & 0xFF;
                    length = (length << 8) + next;
                }
                if (length < 0) {
                    return false;
                }
            }
            return encoding.length == offset + length;
        }

        @Deprecated
        private String getPrivateKey(String password) throws Exception {
            return decrypt(password);
        }
        @Deprecated
        private String decrypt(String password) throws Exception {
            if (password == null) {
                throw new Exception("SM2File password should not be null");
            }
            if (this.privateInfo == null) {
                throw new Exception("SM2File invalid : privateInfo=null");
            }
            ASN1OctetString priOctString = (ASN1OctetString) this.privateInfo.getObjectAt(2);
            byte[] encryptedData;
            try {
                encryptedData = priOctString.getOctets();
            } catch (Exception e) {
                throw new Exception("SM2File decoding failure", e);
            }
            byte[] dBytes = SM4DecryptDBytes(password, encryptedData);

            return Hex.toHexString(dBytes).substring(0, 64);
        }

        private byte[] SM4DecryptDBytes(String password, byte[] encryptedData) throws Exception {
            if ((password == null) || (password.length() == 0)) {
                throw new Exception("SM2File password should not be null");
            }
            byte[] passwordBytes;
            try {
                passwordBytes = password.getBytes("UTF8");
            } catch (UnsupportedEncodingException e) {
                throw new Exception("SM2File password decoding failure", e);
            }
            if ((encryptedData == null) || (encryptedData.length == 0)) {
                throw new Exception("SM2File encryptedData should not be null");
            }
            if ((encryptedData.length < 32) || (encryptedData.length > 64)) {
                throw new Exception("SM2File EncryptedData required length in [32-64] ");
            }
            byte[] encoding = null;
            if ((encryptedData.length == 32) || (encryptedData.length == 48)) {
                encoding = encryptedData;
            } else {
                try {
                    encoding = Base64.decode(encryptedData);
                } catch (Exception e) {
                    throw new Exception("SM2File EncryptedData required base64 ");
                }
            }
            byte[] iv;
            byte[] sm4;
            try {
                byte[] hash = KDF(passwordBytes);
                iv = new byte[16];
                System.arraycopy(hash, 0, iv, 0, 16);
                sm4 = new byte[16];
                System.arraycopy(hash, 16, sm4, 0, 16);
            } catch (Exception e) {
                throw new Exception("SM2File KDF failure", e);
            }
            try {
                PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(new CBCBlockCipher(new SM4Engine()),
                        new PKCS7Padding());
                ParametersWithIV params = new ParametersWithIV(new KeyParameter(sm4), iv);
                cipher.init(false, params);

                int outLength = cipher.getOutputSize(encoding.length);
                byte[] out = new byte[outLength];
                int dataLength = cipher.processBytes(encoding, 0, encoding.length, out, 0);
                int lastLength = cipher.doFinal(out, dataLength);
                int realLength = dataLength + lastLength;
                byte[] dBytes = null;
                if (realLength < outLength) {
                    dBytes = new byte[realLength];
                    System.arraycopy(out, 0, dBytes, 0, realLength);
                }
                return out;
            } catch (DataLengthException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalDataLength", e);
            } catch (IllegalArgumentException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalArgument", e);
            } catch (IllegalStateException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalState", e);
            } catch (InvalidCipherTextException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with InvalidCipherText", e);
            } catch (Exception e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure", e);
            }
        }

        private byte[] KDF(byte[] z) {
            byte[] ct = {0, 0, 0, 1};
            SM3Digest sm3 = new SM3Digest();
            sm3.update(z, 0, z.length);
            sm3.update(ct, 0, ct.length);
            byte[] hash = new byte[32];
            sm3.doFinal(hash, 0);
            return hash;
        }

        @Override
        public ASN1Primitive toASN1Primitive() {
            return null;
        }

    }
}