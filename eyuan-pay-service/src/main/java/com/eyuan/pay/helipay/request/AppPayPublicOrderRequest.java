package com.eyuan.pay.helipay.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.*;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppPayPublicOrderRequest {

    @JSONField(name = "P1_bizType")
    private String P1_bizType;
    @J<PERSON>NField(name = "P2_orderId")
    private String P2_orderId;
    @JSONField(name = "P3_customerNumber")
    private String P3_customerNumber;
    @JSONField(name = "P4_payType")
    private String P4_payType;
    @JSONField(name = "P5_appid")
    private String P5_appid;
    @JSONField(name = "P6_deviceInfo")
    private String P6_deviceInfo;
    @JSONField(name = "P7_isRaw")
    private String P7_isRaw;
    @JSONField(name = "P8_openid")
    private String P8_openid;
    @JSONField(name = "P9_orderAmount")
    private String P9_orderAmount;
    @JSONField(name = "P10_currency")
    private String P10_currency;
    @JSONField(name = "P11_appType")
    private String P11_appType;
    @JSONField(name = "P12_notifyUrl")
    private String P12_notifyUrl;
    @JSONField(name = "P13_successToUrl")
    private String P13_successToUrl;
    @JSONField(name = "P14_orderIp")
    private String P14_orderIp;
    @JSONField(name = "P15_goodsName")
    private String P15_goodsName;
    @JSONField(name = "P16_goodsDetail")
    private String P16_goodsDetail;
    @JSONField(name = "P17_limitCreditPay")
    private String P17_limitCreditPay;
    @JSONField(name = "P18_desc")
    private String P18_desc;
    @JSONField(name = "P19_subscribeAppId")
    private String P19_subscribeAppId;
    /**特殊参数,排除签名*/
    @JSONField(name = "P20_subMerchantId")
    private String P20_subMerchantId;
    @JSONField(name = "P21_goodsTag")
    private String P21_goodsTag;
    @JSONField(name = "P22_guid")
    private String P22_guid;
    @JSONField(name = "P23_marketingRule")
    private String P23_marketingRule;
    @JSONField(name = "P24_identity")
    private String P24_identity;
    private String timeExpire;
    /**
     * 花呗分期数
     * @see extendParams
     */
    @Deprecated
    private String hbfqNum;
    /**
     * 支付宝行业数据回流信息,排除签名
     *
     * @see extendParams
     */
    @Deprecated
    private String industryRefluxInfo;
    /**
     * 支付宝点餐码 排除签名
     *
     * @see extendParams
     */
    @Deprecated
    private String foodOrderType;
    /**排除签名*/
    private String termInfo;
    /**
     * 银联js用户id类型
     * userAuthCode:表示为临时授权码;openId:表示为用户id
     * 不送默认是userAuthCode
     * 排除签名
     */
    private String openIdType;
    /**商户自定义门店编码,排除签名*/
    private String storeId;
    /**支付宝店铺编号*/
    private String alipayStoreId;
    private String ruleJson;
    private String splitBillType;
    /**
     * 终端绑定号 排除签名
     */
    private String terminalSysBindNo;
    /**
     * 场景信息 排除签名
     */
    private String sceneInfo;

    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.SM3WITHSM2;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    /**
     * 服务商pid,不传以平台配置为准(仅银联二维码生效)
     */
    private String pid;
    /**
     * 业务扩展参数(排除签名)
     * 目前是扫码支付宝用:https://open.alipay.com/api
     * ********对{@link #hbfqNum}和{@link #industryRefluxInfo}和{@link #foodOrderType}进行整合,需要兼容存量商户传参
     */
    private String extendParams;
    /**
     * 商户传入业务信息，具体值要和支付宝约定
     */
    private String businessParams;
    /**
     * 传入学校名称和场景名称,支付宝参数
     */
    private String eduSubject;
    /**
     * 用户记账簿编号
     */
    private String boaAccountNo;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 储值订单类型
     * 枚举值:STOREDVALUE
     */
    private String storedValueOrderType;
    /**
     * 银联二维码 
     */
    private String qrcode;
    /**
     * 云梯计划相关-银联支付渠道
     * 01  云闪付微信小程序
     * 02  云闪付微信公众号
     * **/
    private String unionPayChannelType;
    /**
     * 分账计费类型
     * 枚举值 STANDARD：默认标准 INDEPENDENT：独立计费
     */
    private String splitCalcFeeType;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId", "P3_customerNumber",
            "P4_payType", "P5_appid", "P6_deviceInfo", "P7_isRaw", "P8_openid", "P9_orderAmount", "P10_currency",
            "P11_appType", "P12_notifyUrl", "P13_successToUrl", "P14_orderIp", "P15_goodsName", "P16_goodsDetail", "P17_limitCreditPay",
            "P18_desc");
    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("ruleJson", "P23_marketingRule");
}
