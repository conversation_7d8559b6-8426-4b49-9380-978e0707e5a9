package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @Author: sunwh
 * @Date: 2025/5/4 17:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HeliPayTransferResponse {

    /**
     * 交易类型
     */
    private String rt1_bizType;
    /**
     * 返回码
     */
    private String rt2_retCode;
    /**
     * 返回信息
     */
    private String rt3_retMsg;
    /**
     * 金额
     */
    private String rt4_customerNumber;
    private String rt5_orderId;
    private String rt6_serialNumber;
    private String sign;
    /**
     * 签名类型(不参与签名)
     */
    private SignatureType signatureType;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_bizType", "rt2_retCode", "rt4_customerNumber",
            "rt5_orderId", "rt6_serialNumber");
}
