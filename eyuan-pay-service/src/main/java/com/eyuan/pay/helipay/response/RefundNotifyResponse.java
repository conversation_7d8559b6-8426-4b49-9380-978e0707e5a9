package com.eyuan.pay.helipay.response;


import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Set;

/**
 * 扫码退款回调
 */
@Setter
@Getter
@ToString
public class RefundNotifyResponse {

    private String rt1_customerNumber;
    private String rt2_orderId;
    private String rt3_refundOrderId;
    private String rt4_systemSerial;
    private String rt5_status;
    private String rt6_amount;
    private String rt7_currency;
    private String rt8_timestamp;
    /**
     * 退回后收手续费(不参与签名)
     */
    private BigDecimal offlineFee;
    /**
     * 退回实时收手续费(不参与签名)
     */
    private BigDecimal receiverFee;
    /**
     * 退款是否垫付(不参与签名)
     */
    private Boolean withPayAdvanceTrx;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 签名类型(不参与签名)
     */
    private SignatureType signatureType;
    /**
     * sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)
     */
    private String encryptionKey;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_customerNumber", "rt2_orderId",
            "rt3_refundOrderId", "rt4_systemSerial", "rt5_status", "rt6_amount", "rt7_currency", "rt8_timestamp");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("");
}
