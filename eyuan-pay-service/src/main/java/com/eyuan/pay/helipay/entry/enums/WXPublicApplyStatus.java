package com.eyuan.pay.helipay.entry.enums;

public enum WXPublicApplyStatus {
    /**
     *
     */
    NOT_APPLY_YET("尚未报备", 1),

    DOING("处理中", 2),

    SUCCESS("成功", 3),

    FAIL("失败", 4),

    TO_BE_SIGNED("待签署", 5),

    ;

    private String desc;
    private Integer index;

    WXPublicApplyStatus(String desc, Integer index) {
        this.desc = desc;
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getIndex() {
        return index;
    }

}
