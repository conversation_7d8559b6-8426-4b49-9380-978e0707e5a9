package com.eyuan.pay.helipay.response;


import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

@Setter
@Getter
@ToString
public class HeliTransferNotifyResponse {

    /**
     * 交易类型
     */
    private String rt1_bizType;
    /**
     * 返回码
     */
    private String rt2_retCode;
    /**
     * 返回信息
     */
    private String rt3_retMsg;

    /**
     * 商户编号
     */
    private String rt4_customerNumber;

    /**
     * 商户订单号
     */
    private String rt5_orderId;
    /**
     * 平台流水号
     */
    private String rt6_serialNumber;

    /**
     * 打款状态
     *
     * RECEIVE 已接收
     * INIT初始化
     * DOING处理中
     * SUCCESS成功
     * FAIL失败
     * REFUND退款
     */
    private String rt7_orderStatus;

    /**
     * 通知类型
     * ORDER_STATUS:普通通知
     * RETURN_REMITTANCE:退汇通知
     */
    private String rt8_notifyType;

    /**
     * 成功或失败的原因
     */
    private String rt9_reason;
    private String rt10_createDate;
    private String rt11_completeDate;
    private String rt12_amount;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;

    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_bizType", "rt2_retCode",
            "rt3_retMsg", "rt4_customerNumber", "rt5_orderId", "rt6_serialNumber", "rt7_orderStatus", "rt8_notifyType"
            , "rt9_reason","rt10_createDate", "rt11_completeDate");
}
