package com.eyuan.pay.helipay.entry.enums;

/**
 * <AUTHOR>
 * @description 证件持有人类型
 * @date 2022/5/24 12:05
 */
public enum IdHolderType {

    LEGAL("经营者/法人", 0, "65"),
    SUPER("经办人", 1, "66"),

    ;

    IdHolderType(String desc, Integer index, String bussinessCode) {
        this.desc = desc;
        this.index = index;
        this.bussinessCode = bussinessCode;
    }

    private String desc;
    private Integer index;
    private String bussinessCode;

    public String getDesc() {
        return desc;
    }


    public Integer getIndex() {
        return index;
    }

    public String getBussinessCode() {
        return bussinessCode;
    }

}
