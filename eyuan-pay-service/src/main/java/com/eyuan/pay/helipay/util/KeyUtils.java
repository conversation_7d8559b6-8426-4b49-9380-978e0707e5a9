package com.eyuan.pay.helipay.util;

import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.util.Enumeration;

/**
 * @description:
 * @author: zhenghf
 * @since:
 */
public abstract class KeyUtils {

    private KeyUtils() { }


    public static PrivateKey getPrivateKeyByPfx(String pfxPath, String pfxPassword) {
        try {
            KeyStore ks = KeyStore.getInstance("PKCS12",InternalBCHelper.getProvider());
            FileInputStream fis = new FileInputStream(pfxPath);
            // If the keystore password is empty(""), then we have to set
            // to null, otherwise it won't work!!!
            char[] nPassword = null;
            if ((pfxPassword == null) || pfxPassword.trim().equals("")) {
                nPassword = null;
            } else {
                nPassword = pfxPassword.toCharArray();
            }
            ks.load(fis, nPassword);
            fis.close();
            Enumeration enumas = ks.aliases();
            String keyAlias = null;
            if (enumas.hasMoreElements())// we are readin just one certificate.
            {
                keyAlias = (String) enumas.nextElement();
            }
            PrivateKey prikey = (PrivateKey) ks.getKey(keyAlias, nPassword);
            return prikey;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}