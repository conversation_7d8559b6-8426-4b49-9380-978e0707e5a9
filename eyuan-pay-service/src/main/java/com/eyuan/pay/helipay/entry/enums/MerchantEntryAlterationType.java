package com.eyuan.pay.helipay.entry.enums;


import java.util.Arrays;
import java.util.List;

public enum MerchantEntryAlterationType {

    SETTLE_BANKCARD("结算卡", 1),
    MERCHANT_INFO("商户信息", 2),
    MERCHANT_CREDENTIAL("资质", 3),
    MERCHANT_INFO_SYN("商户信息(新)", 4),
    ;
    public static final List<MerchantEntryAlterationType> ALTERATION_TYPES = Arrays.asList(SETTLE_BANKCARD, MERCHANT_INFO, MERCHANT_CREDENTIAL);

    private final String desc;
    private final Integer index;

    MerchantEntryAlterationType(String desc, Integer index) {
        this.desc = desc;
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getIndex() {
        return index;
    }
}
