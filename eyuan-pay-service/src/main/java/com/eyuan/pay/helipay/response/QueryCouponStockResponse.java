package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

/**
 * @create 2019-02-22 15:13
 */
@Setter
@Getter
@ToString
public class QueryCouponStockResponse {
    private String biz_type;
    private String customer_number;
    private String mch_id;
    private String device_info;
    private String nonce_str;
    private String result_code;
    private String err_code;
    private String err_code_des;
    private String coupon_stock_id;
    private String coupon_name;
    private String coupon_value;
    private String coupon_mininumn;
    private String coupon_stock_status;
    private String coupon_total;
    private String max_quota;
    private String is_send_num;
    private String begin_time;
    private String end_time;
    private String create_time;
    private String coupon_budget;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("biz_type", "customer_number",
            "mch_id", "device_info", "nonce_str", "result_code", "err_code", "err_code_des", "coupon_stock_id",
            "coupon_name", "coupon_value", "coupon_mininumn", "coupon_stock_status", "coupon_total", "max_quota",
            "is_send_num", "begin_time", "end_time", "create_time", "coupon_budget");
}
