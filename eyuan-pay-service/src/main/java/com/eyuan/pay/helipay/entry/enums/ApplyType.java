package com.eyuan.pay.helipay.entry.enums;

public enum ApplyType {
    /**
     * @deprecated
     */
    BlueOcean("蓝海绿洲报名", 1),

    PAYMENT("缴费类", 2),

    PUBLIC_WELFARE("公益类", 3),

    INSURANCE("保险教育类", 4),

    ACADEMY("私立院校类", 5),

    ONLINE("线上类", 6),

    VIRTUAL("虚拟类", 7),
    ;
    private final String desc;
    private final Integer index;


    ApplyType(String desc, Integer index) {
        this.desc = desc;
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getIndex() {
        return index;
    }
}
