package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
public class AppCreateOrderResponse {
    /**
     * 扫码主被扫响应参数
     */
    private String rt1_bizType;
    private String rt2_retCode;
    private String rt3_retMsg;            //排除签名
    private String rt4_customerNumber;
    private String rt5_orderId;
    private String rt6_serialNumber;
    private String rt7_payType;
    private String rt8_qrcode;
    private String rt9_wapurl;
    private String rt10_orderAmount;
    private String rt11_currency;
    private String rt12_openId;             //排除签名
    private String rt13_orderStatus;        //排除签名
    private String rt14_fundBillList;       //排除签名
    private String rt15_channelRetCode;     //排除签名
    private String rt16_outTransactionOrderId;  //排除签名
    private String rt17_bankType;               //排除签名
    private String rt18_subOpenId;              //排除签名
    private String onlineCardType;              //排除签名
    /**
     * 排除签名
     */
    private String subMerchantNo;
    /**
     * 后收手续费(不参与签名)
     */
    private BigDecimal offlineFee;
    /**
     * 实时收手续费(不参与签名)
     */
    private BigDecimal receiverFee;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 可分账金额
     */
    private BigDecimal splittableAmount;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_bizType", "rt2_retCode",
            "rt4_customerNumber", "rt5_orderId", "rt6_serialNumber", "rt7_payType",
            "rt8_qrcode", "rt9_wapurl", "rt10_orderAmount", "rt11_currency");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("rt20_marketingRule");
}
