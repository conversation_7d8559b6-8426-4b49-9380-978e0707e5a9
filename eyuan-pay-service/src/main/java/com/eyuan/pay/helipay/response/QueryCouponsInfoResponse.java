package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

/**
 * @create 2019-02-22 15:15
 */
@Setter
@Getter
@ToString
public class QueryCouponsInfoResponse {
    private String biz_type;
    private String customer_number;
    private String mch_id;
    private String sub_mch_id;
    private String device_info;
    private String nonce_str;
    private String result_code;
    private String err_code;
    private String err_code_des;
    private String coupon_stock_id;
    private String coupon_id;
    private String coupon_value;
    private String coupon_mininum;
    private String coupon_name;
    private String coupon_state;
    private String coupon_desc;
    private String coupon_use_value;
    private String coupon_remain_value;
    private String send_source;
    private String is_partial_use;
    private String return_code;
    private String return_msg;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("biz_type", "customer_number",
            "mch_id", "sub_mch_id", "device_info", "nonce_str", "result_code", "err_code", "err_code_des",
            "coupon_stock_id", "coupon_id", "coupon_value", "coupon_mininum", "coupon_name", "coupon_state", "coupon_desc",
            "coupon_use_value", "coupon_remain_value", "send_source", "is_partial_use", "return_code", "return_msg");
}
