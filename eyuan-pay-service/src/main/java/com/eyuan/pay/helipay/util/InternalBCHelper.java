package com.eyuan.pay.helipay.util;

import org.bouncycastle.jcajce.util.BCJcaJceHelper;
import org.bouncycastle.jcajce.util.JcaJceHelper;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.Provider;
import java.security.Security;

public class InternalBCHelper {

    static final JcaJceHelper BC_HELPER;

    static {
        Provider provider = getProvider();
        if (provider == null) {
            provider = new BouncyCastleProvider();
            Security.addProvider(provider);
        }
        BC_HELPER = new BCJcaJceHelper();
    }

    @SuppressWarnings("UnusedReturnValue")
    static Provider getProvider() {
        return Security.getProvider(BouncyCastleProvider.PROVIDER_NAME);
    }


}
