package com.eyuan.pay.helipay.client;

import com.dtflys.forest.annotation.*;
import com.eyuan.pay.helipay.interceptor.HeliAppPayInterceptor;
import com.eyuan.pay.helipay.response.HeliPaySettlementResponse;
import com.eyuan.pay.helipay.response.HeliPayTransferResponse;

import java.util.Map;

/**
 * @Author: sunwh
 * @Date: 2025/4/7 16:23
 */
@BaseRequest(interceptor = HeliAppPayInterceptor.class)
public interface HeliTransferClient {

    /**
     * 合利宝-代付接口
     * @param request
     * @return
     */
    @Post(url = "{url}",contentType="application/x-www-form-urlencoded")
    String transfer(@Var("url") String url,@FormBody Map<String,String> request);

    /**
     * 合利宝-结算接口
     * @param request
     * @return
     */
    @Post(url = "{url}")
    HeliPaySettlementResponse settle(@Var("url") String url, @FormBody Map<String,String> request);
}
