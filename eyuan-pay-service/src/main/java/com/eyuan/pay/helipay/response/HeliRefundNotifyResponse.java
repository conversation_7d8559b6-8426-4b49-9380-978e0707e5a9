package com.eyuan.pay.helipay.response;


import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

@Setter
@Getter
@ToString
public class HeliRefundNotifyResponse {

    private String rt1_customerNumber;
    private String rt2_orderId;
    private String rt3_refundOrderId;
    private String rt4_systemSerial;
    private String rt5_status;
    private String rt6_amount;
    private String rt7_currency;
    private String rt8_timestamp;
    private String rt9_refundOrderCompleteDate;
    private String rt10_refundChannelOrderNum;
    private String rt11_desc;
    private String rt12_refundOrderAttribute;
    private String rt13_appPayType;
    private String rt14_payType;
    private String controlType;
    private String refundMarketingRule;
    private String refundPromotionDetail;
    private String debitAmount;
    private String refundCashAmount;
    private String retReasonDesc;
    private String upAddData;
    private String productFee;
    private String feeAccountAmt;
    private String ruleJson;
    private String withPayAdvanceTrx;
    private String receiverFee;
    private String offlineFee;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_customerNumber", "rt2_orderId",
            "rt3_refundOrderId", "rt4_systemSerial", "rt5_status", "rt6_amount", "rt7_currency", "rt8_timestamp");

}
