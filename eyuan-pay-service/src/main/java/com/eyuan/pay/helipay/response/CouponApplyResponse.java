package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

/**
 * @create 2019-02-22 15:05
 */
@Setter
@Getter
@ToString
public class CouponApplyResponse {
    private String bizType;
    private String customer_number;
    private String mch_id;
    private String device_info;
    private String nonce_str;
    private String result_code;
    private String err_code;
    private String err_code_des;
    private String coupon_stock_id;
    private String resp_count;
    private String success_count;
    private String failed_count;
    private String openid;
    private String coupon_id;
    private String ret_code;
    private String ret_msg;
    private String return_code;
    private String return_msg;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("bizType",
            "customer_number", "mch_id", "device_info", "nonce_str",
            "result_code", "err_code", "err_code_des", "coupon_stock_id",
            "resp_count", "success_count", "failed_count", "openid",
            "coupon_id", "ret_code", "ret_msg", "return_code", "return_msg");
}
