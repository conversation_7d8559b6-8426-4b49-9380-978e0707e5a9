package com.eyuan.pay.helipay.response;

import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Set;

@Setter
@Getter
@ToString
public class AppPayRefundQueryResponse {


    private String rt1_bizType;
    private String rt2_retCode;
    private String rt3_retMsg;
    private String rt4_customerNumber;
    private String rt5_orderId;
    private String rt6_refundOrderNum;
    private String rt7_serialNumber;
    private String rt8_orderStatus;
    private String rt9_amount;
    private String rt10_currency;

    private String rt11_refundOrderCompleteDate;
    private String rt12_refundChannelOrderNum;
    private String rt13_desc;
    private String rt14_refundOrderAttribute;
    private String rt15_appPayType;
    /**排除签名*/
    private String upAddData;
    /**
     * 退回后收手续费(不参与签名)
     */
    private BigDecimal offlineFee;
    /**
     * 退回实时收手续费(不参与签名)
     */
    private BigDecimal receiverFee;
    /**
     * 退款是否垫付(不参与签名)
     */
    private Boolean withPayAdvanceTrx;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("rt1_bizType", "rt2_retCode", "rt4_customerNumber",
            "rt5_orderId", "rt6_refundOrderNum", "rt7_serialNumber", "rt8_orderStatus", "rt9_amount", "rt10_currency");
    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of();
}
