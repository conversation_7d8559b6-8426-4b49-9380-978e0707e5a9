package com.eyuan.pay.helipay.enums;

import com.eyuan.pay.utils.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2019-06-14
 */
@Getter
@AllArgsConstructor
public enum HeliPayTradeStatusEnum {

	/**
	 * 接收
	 */
	INIT("INIT", OrderStatusEnum.INIT.getStatus()),

	/**
	 * 处理中
	 */
	DOING("DOING", OrderStatusEnum.INIT.getStatus()),


	/**
	 * 关闭
	 */
	CLOSE("CLOSE", OrderStatusEnum.FAIL.getStatus()),

	/**
	 * 撤销
	 */
	CANCEL("CANCEL", OrderStatusEnum.FAIL.getStatus()),

	/**
	 * 微信支付成功
	 */
	SUCCESS("SUCCESS", OrderStatusEnum.SUCCESS.getStatus()),

	/**
	 * 微信支付失败
	 */
	FAIL("FAIL", OrderStatusEnum.FAIL.getStatus());


	/**
	 * 描述
	 */
	private String description;
	/**
	 * 描述
	 */
	private String status;
}
