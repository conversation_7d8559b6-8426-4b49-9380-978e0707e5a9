package com.eyuan.pay.helipay.util;


import cn.hutool.crypto.symmetric.SM4;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

public abstract class SM4Utils {

    private SM4Utils() {}

    private final static IvParameterSpec API_IV_SPEC;

    static {
        InternalBCHelper.getProvider();
        API_IV_SPEC = new IvParameterSpec(Base64.decode("AQ4Zvt54xKn9QaW86ZzWdg==".getBytes()));
    }

    /**
     * @return 加密串（base64 版）
     */
    public static String encrypt(byte[] key, byte[] iv, String data) {
        byte[] bytes = encrypt(key, iv, data.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.encode(bytes));
    }

    public static String decrypt(byte[] key, byte[] iv, String base64) {
        byte[] decoded = Base64.decode(base64.getBytes());
        byte[] bytes = decrypt(key, iv, decoded);
        return StringUtils.toEncodedString(bytes, StandardCharsets.UTF_8);
    }

    public static byte[] encrypt(byte[] key, byte[] iv, byte[] data) {
        SecretKeySpec keySpec = new SecretKeySpec(key, "SM4");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        return encrypt(keySpec, ivSpec, data);
    }

    public static byte[] decrypt(byte[] key, byte[] iv, byte[] data) {
        SecretKeySpec keySpec = new SecretKeySpec(key, "SM4");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        return decrypt(keySpec, ivSpec, data);
    }


    public static byte[] encrypt(SecretKey key, IvParameterSpec iv, byte[] data) {
        SM4 sm4 = new SM4("CBC", "PKCS7Padding", key, iv);
        return sm4.encrypt(data);
    }

    public static byte[] decrypt(SecretKey key, IvParameterSpec iv, byte[] data) {
        SM4 sm4 = new SM4("CBC", "PKCS7Padding", key, iv);
        return sm4.decrypt(data);
    }

    public static String encryptBase64(SecretKey key, String data) {
        byte[] bytes = encrypt(key, data.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.encode(bytes));
    }

    public static byte[] encrypt(SecretKey key, byte[] data) {
        SM4 sm4 = new SM4("CBC", "PKCS7Padding", key, API_IV_SPEC);
        return sm4.encrypt(data);
    }

    public static byte[] decrypt(SecretKey key, byte[] data) {
        SM4 sm4 = new SM4("CBC", "PKCS7Padding", key, API_IV_SPEC);
        return sm4.decrypt(data);
    }


    public static SecretKeySpec generateSm4Key() {
        KeyGenerator generator = null;
        try {
            generator = InternalBCHelper.BC_HELPER.createKeyGenerator("SM4");
        } catch (Exception e) {
            ExceptionUtils.rethrow(e);
        }
        SecretKey originKey = generator.generateKey();
        return new SecretKeySpec(originKey.getEncoded(), "SM4");
    }

    public static SecretKeySpec toSecretKeySpec(byte[] bytes) {
        return new SecretKeySpec(bytes, "SM4");
    }

}
