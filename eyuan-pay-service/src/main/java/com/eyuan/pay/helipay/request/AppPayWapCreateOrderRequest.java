package com.eyuan.pay.helipay.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.eyuan.pay.helipay.enums.SignatureType;
import com.google.common.collect.ImmutableSet;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

@Data
public class AppPayWapCreateOrderRequest {

    @JSONField(name = "P1_bizType")
    private String P1_bizType;
    @JSONField(name = "P2_orderId")
    private String P2_orderId;
    @JSONField(name = "P3_customerNumber")
    private String P3_customerNumber;
    @J<PERSON>NField(name = "P4_orderAmount")
    private String P4_orderAmount;
    @JSONField(name = "P5_currency")
    private String P5_currency;
    @JSONField(name = "P6_orderIp")
    private String P6_orderIp;
    @JSONField(name = "P7_notifyUrl")
    private String P7_notifyUrl;
    @JSONField(name = "P8_appPayType")
    private String P8_appPayType;
    @JSONField(name = "P9_payType")
    private String P9_payType;
    @JSONField(name = "P10_appName")
    private String P10_appName;
    @JSONField(name = "P11_deviceInfo")
    private String P11_deviceInfo;
    @JSONField(name = "P12_applicationId")
    private String P12_applicationId;
    @JSONField(name = "P13_goodsName")
    private String P13_goodsName;
    @JSONField(name = "P14_goodsDetail")
    private String P14_goodsDetail;
    @JSONField(name = "P15_desc")
    private String P15_desc;
    @JSONField(name = "P16_limitCreditPay")
    private String P16_limitCreditPay;
    @JSONField(name = "P17_goodsTag")
    private String P17_goodsTag;
    @JSONField(name = "P18_guid")
    private String P18_guid;
    @JSONField(name = "P19_marketingRule")
    private String P19_marketingRule;
    @JSONField(name = "P20_identity")
    private String P20_identity;
    private String requireRiskCommon;
    private String splitBillType;
    private String ruleJson;
    private String isRaw;
    private String appId;

    /**
     * 终端绑定号 排除签名
     */
    private String terminalSysBindNo;

    /**
     * 场景信息 排除签名
     */
    private String sceneInfo;
    /**
     * 成功跳转URL,非原生模式下且APP内嵌H5网页时此参数有效
     * 目前针对支付宝用
     */
    private String successToUrl;
    private String subMerchantId;

    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.SM3WITHSM2;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    /**
     * 用户记账簿编号
     */
    private String boaAccountNo;
    /**
     * 待分账金额
     */
    private BigDecimal presetSplitAmount;
    /**
     * 储值订单类型
     * 枚举值:STOREDVALUE
     */
    private String storedValueOrderType;
    /**
     * 分账计费类型
     * 枚举值 STANDARD：默认标准 INDEPENDENT：独立计费
     */
    private String splitCalcFeeType;
    private String sign;


    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId", "P3_customerNumber",
            "P4_orderAmount", "P5_currency", "P6_orderIp", "P7_notifyUrl", "P8_appPayType", "P9_payType", "P10_appName",
            "P11_deviceInfo", "P12_applicationId", "P13_goodsName", "P14_goodsDetail", "P15_desc");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("ruleJson", "P19_marketingRule");
}
