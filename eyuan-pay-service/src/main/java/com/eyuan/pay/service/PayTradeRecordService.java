package com.eyuan.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eyuan.pay.entity.PayTradeRecord;

import java.util.List;

/**
 * 交易记录
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface PayTradeRecordService extends IService<PayTradeRecord> {

    /**
     * 根据交易号查询交易记录
     * @param tradeNo 交易号
     * @return 交易记录
     */
    PayTradeRecord getByTradeNo(String tradeNo);
    
    /**
     * 根据三方交易号查询交易记录
     * @param thirdTradeNo 三方交易号
     * @return 交易记录列表
     */
    PayTradeRecord findByThirdTradeNo(String thirdTradeNo);
    
    /**
     * 根据业务订单号查询交易记录
     * @param orderNo 业务订单号
     * @return 交易记录列表
     */
    List<PayTradeRecord> findByOrderNo(String orderNo);
}