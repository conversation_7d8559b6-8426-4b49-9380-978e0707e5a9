package com.eyuan.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dog.common.core.annotation.EnumBool;
import com.dog.common.core.constant.CacheConstants;
import com.dog.common.core.context.TenantHolder;
import com.dog.common.core.util.AssertUtil;
import com.dog.redis.RedisHelper;
import com.eyuan.pay.dto.*;
import com.eyuan.pay.entity.PayChannelConfig;
import com.eyuan.pay.enums.PayChannelEnum;
import com.eyuan.pay.enums.PaySceneEnum;
import com.eyuan.pay.mapper.PayChannelConfigMapper;
import com.eyuan.pay.service.PayChannelConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付渠道配置
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@Service
@AllArgsConstructor
@Slf4j
public class PayChannelConfigServiceImpl extends ServiceImpl<PayChannelConfigMapper, PayChannelConfig> implements PayChannelConfigService {

    private final RedisHelper redisHelper;

    /**
     * 保存或更新支付渠道配置
     *
     * @param payChannelConfig 支付渠道配置
     * @return 是否成功
     */
    @Override
    @CacheEvict(value = CacheConstants.PAY_CHANNEL_CONFIG_KEY, key = "T(com.dog.common.core.context.TenantHolder).getTenantId() + ':' + #payChannelConfig.channelId")
    public Boolean saveOrUpdateChannelConfig(PayChannelConfig payChannelConfig) {
        log.info("[支付渠道配置] 保存或更新, channelId:{}, id:{}", payChannelConfig.getChannelId(), payChannelConfig.getId());

        // 判断是新增还是更新
        if (payChannelConfig.getId() != null) {
            // 更新
            return updateById(payChannelConfig);
        } else {
            // 新增前检查渠道ID是否已存在
            PayChannelConfig existConfig = findByChannelId(payChannelConfig.getChannelId());
            if (existConfig != null) {
                // 如果已存在相同渠道ID的配置，则更新
                payChannelConfig.setId(existConfig.getId());
                return updateById(payChannelConfig);
            } else {
                // 新增
                return save(payChannelConfig);
            }
        }
    }

    /**
     * 根据条件查询支付渠道配置列表
     *
     * @param wrapper 查询条件
     * @return 支付渠道配置列表
     */
    @Override
    public List<PayChannelConfig> listPayChannelConfig(LambdaQueryWrapper<PayChannelConfig> wrapper) {
        return this.getBaseMapper().selectList(wrapper);
    }

    /**
     * 查询所有支付渠道配置
     *
     * @return 所有支付渠道配置
     */
    @Override
    public List<PayChannelConfig> listAll() {
        return this.getBaseMapper().selectByMap(new HashMap<>());
    }

    /**
     * 根据渠道ID查询支付渠道配置
     *
     * @param channelId 渠道ID
     * @return 支付渠道配置
     */
    @Override
//    @Cacheable(value = CacheConstants.PAY_CHANNEL_CONFIG_KEY, key = "T(com.dog.common.core.context.TenantHolder).getTenantId() + ':' + #channelId", unless = "#result == null")
    public PayChannelConfig findByChannelId(String channelId) {
        String tenantId = TenantHolder.getTenantId();
        String payChannelConfigCache = redisHelper.get(CacheConstants.PAY_CHANNEL_CONFIG_KEY + tenantId + ":" + channelId);
        if(StrUtil.isNotBlank(payChannelConfigCache)){
            return JSONUtil.toBean(payChannelConfigCache, PayChannelConfig.class);
        }
        LambdaQueryWrapper<PayChannelConfig> wrapper = Wrappers.<PayChannelConfig>lambdaQuery()
                .eq(PayChannelConfig::getChannelId, channelId);

        return this.getBaseMapper().selectOne(wrapper);
    }

    /**
     * 删除支付渠道配置时清除缓存
     * 缓存键的格式是：pay_channel_config:租户ID:渠道ID
     *
     * @param id 配置ID
     * @return 是否成功
     */
    @Override
    public Boolean removeConfigById(Long id) {
        // 先查询出配置，以获取渠道ID
        PayChannelConfig config = getById(id);
        if (config != null) {
            // 先清除缓存
            evictCache(config.getChannelId());
            // 再删除数据
            return removeById(id);
        }
        return false;
    }

    @Override
    public PaySceneDTO getPaySceneConfig(String channelId,String payType,String payScene) {
        AssertUtil.notBlank(channelId, "支付渠道ID不能为空");
        AssertUtil.notBlank(payScene, "支付场景不能为空");
        PaySceneEnum paySceneEnum = PaySceneEnum.getByValue(payScene);
        AssertUtil.notNull(paySceneEnum, "支付场景有误");
        if (PayChannelEnum.HELIPAY.getValue().equals(channelId)) {
            AssertUtil.notBlank(payType, "支付类型不能为空");
        }


        PayChannelConfig payChannelConfig = this.findByChannelId(channelId);
        AssertUtil.notNull(payChannelConfig, "支付渠道配置不存在");
        AssertUtil.isTrue(EnumBool.TRUE.getValue().equals(payChannelConfig.getState()), "支付渠道配置已禁用");

        PayChannelEnum payChannelEnum = PayChannelEnum.getByValue(channelId);
        AssertUtil.notNull(payChannelEnum, "支付渠道配置有误");

        PaySceneDTO paySceneDTO = BeanUtil.toBean(payChannelConfig, PaySceneDTO.class);

        // 解析param参数获取支付场景配置
        String param = payChannelConfig.getParam();
        switch (payChannelEnum) {
            case WECHAT_PAY:
                // 微信支付场景配置解析
                List<WechatPaySceneConfig> sceneConfigs = JSONUtil.toList(param, WechatPaySceneConfig.class);
                for (WechatPaySceneConfig sceneConfig : sceneConfigs) {
                    if (sceneConfig.getChannelType().equals(payType) && payScene.equals(sceneConfig.getSceneType())
                            && sceneConfig.getEnabled()) {
                        paySceneDTO.setAppId(sceneConfig.getAppId());
                        paySceneDTO.setSubAppId(sceneConfig.getSubAppId());
                        paySceneDTO.setPayType(sceneConfig.getChannelType());
                        return paySceneDTO;
                    }
                }
                AssertUtil.isTrue(false, "未找到匹配的微信支付场景");
                break;

            case ALIPAY:
                // 支付宝支付场景配置解析
                AlipaySceneConfig alipayConfig = JSONUtil.toBean(param, AlipaySceneConfig.class);
                paySceneDTO.setPrivateKey(alipayConfig.getPrivateKey());
                paySceneDTO.setPublicKey(alipayConfig.getPublicKey());
                List<PaySceneConfig> aliPayScenes = alipayConfig.getScenes();
                for (PaySceneConfig scene : aliPayScenes) {
                    if (scene.getChannelType().equals(payType) && payScene.equals(scene.getSceneType()) && scene.getEnabled()) {
                        paySceneDTO.setAppId(scene.getAppId());
                        paySceneDTO.setPayType(scene.getChannelType());
                        return paySceneDTO;
                    }
                }
                AssertUtil.isTrue(false, "未找到匹配的支付宝支付场景");
                break;

            case HELIPAY:
                // 合利宝支付场景配置解析
                List<HeliPaySceneConfig> heliPayScenes = JSONUtil.toList(param, HeliPaySceneConfig.class);
                for (HeliPaySceneConfig sceneConfig : heliPayScenes) {
                    if (sceneConfig.getChannelType().equals(payType) && payScene.equals(sceneConfig.getSceneType()) && sceneConfig.getEnabled()) {
                        paySceneDTO.setAppId(sceneConfig.getAppId());
                        paySceneDTO.setPayType(sceneConfig.getChannelType());
                        return paySceneDTO;
                    }
                }
                AssertUtil.isTrue(false, "未找到匹配的合利宝支付场景");
                break;
        }

        return null;
    }

    @Override
    public PayChannelConfig getByChannelId(String channelId) {
        PayChannelConfig payChannelConfig = this.findByChannelId(channelId);
        AssertUtil.notNull(payChannelConfig, "支付渠道配置不存在");
        return payChannelConfig;
    }

    @Override
    public String getMerchantKey(String channelId, String merchantId) {
        List<PayChannelConfig> payChannelConfigs = this.baseMapper.selectList(new LambdaQueryWrapper<PayChannelConfig>().eq(PayChannelConfig::getChannelId , channelId).eq(PayChannelConfig :: getMerchantId, merchantId));
        if (CollectionUtil.isNotEmpty(payChannelConfigs)) {
            return payChannelConfigs.get(0).getMerchantKey();
        } else {
            return null;
        }
    }


    /**
     * 清除指定渠道ID的缓存
     *
     * @param channelId 渠道ID
     */
    @CacheEvict(value = CacheConstants.PAY_CHANNEL_CONFIG_KEY, key = "T(com.dog.common.core.context.TenantHolder).getTenantId() + ':' + #channelId")
    public void evictCache(String channelId) {
        log.info("[支付渠道配置] 清除缓存, channelId:{}", channelId);
    }
}
