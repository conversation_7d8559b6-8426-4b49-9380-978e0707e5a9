package com.eyuan.pay.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.handler.PayOrderHandler;
import com.eyuan.pay.mapper.PayGoodsOrderMapper;
import com.eyuan.pay.service.PayGoodsOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 商品
 *
 * <AUTHOR>
 * @date 2019-05-28 23:58:27
 */
@Slf4j
@Service
@AllArgsConstructor
public class PayGoodsOrderServiceImpl extends ServiceImpl<PayGoodsOrderMapper, PayGoodsOrder> implements PayGoodsOrderService {
	private final Map<Integer, PayOrderHandler> payOrderHandlerMap;
	private final HttpServletRequest request;


	/**
	 * 下单购买
	 *
	 * @param goodsOrder
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> buy(PayGoodsOrder goodsOrder) {
		String ua = request.getHeader(HttpHeaders.USER_AGENT);
		PayToolEnum payToolEnum = PayToolEnum.getChannel(ua);
		PayOrderHandler orderHandler = payOrderHandlerMap.get(payToolEnum.value());
		goodsOrder.setGoodsName("测试产品");
		goodsOrder.setGoodsId("10001");
		Object params = orderHandler.handle(goodsOrder);

		Map<String, Object> result = new HashMap<>(4);
		result.put("channel", payToolEnum.name());
		result.put("goods", goodsOrder);
		result.put("params", params);
		return result;
	}

	@Override
	public PayGoodsOrder getByPayOrderId(String payOrderId) {
		Assert.notBlank(payOrderId, "参数错误，支付订单号不能为空");
		return this.baseMapper.getByPayOrderId(payOrderId);
	}

	@Override
	public PayGoodsOrder getByPayOrderIdAndValid(String payOrderId) {
		PayGoodsOrder payGoodsOrder = this.getByPayOrderId(payOrderId);
		Assert.notNull(payGoodsOrder, "商品支付订单不存在");
		return payGoodsOrder;
	}

}
