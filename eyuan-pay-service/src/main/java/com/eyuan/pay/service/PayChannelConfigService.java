package com.eyuan.pay.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eyuan.pay.dto.PaySceneSearchDTO;
import com.eyuan.pay.dto.PaySceneDTO;
import com.eyuan.pay.entity.PayChannelConfig;

import java.util.List;

/**
 * 支付渠道配置
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
public interface PayChannelConfigService extends IService<PayChannelConfig> {

    /**
     * 保存或更新支付渠道配置
     *
     * @param payChannelConfig 支付渠道配置
     * @return 是否成功
     */
    Boolean saveOrUpdateChannelConfig(PayChannelConfig payChannelConfig);

    /**
     * 根据条件查询支付渠道配置列表
     *
     * @param wrapper 查询条件
     * @return 支付渠道配置列表
     */
    List<PayChannelConfig> listPayChannelConfig(LambdaQueryWrapper<PayChannelConfig> wrapper);

    /**
     * 查询所有支付渠道配置
     *
     * @return 所有支付渠道配置
     */
    List<PayChannelConfig> listAll();

    /**
     * 根据渠道ID查询支付渠道配置
     *
     * @param channelId 渠道ID
     * @return 支付渠道配置
     */
    PayChannelConfig findByChannelId(String channelId);

    /**
     * 根据ID删除支付渠道配置
     * @param id
     * @return
     */
    Boolean removeConfigById(Long id);

    /**
     * 查询具体支付场景的配置
     *
     * @param channelId 支付渠道
     * @param payType 支付类型
     * @param payScene 支付场景
     * @return 支付渠道配置
     */
    PaySceneDTO getPaySceneConfig(String channelId,String payType,String payScene);

    /**
     * 查询支付配置
     * @param channelId
     * @return
     */
    PayChannelConfig getByChannelId(String channelId);

    /**
     * 查询支付验签Key
     * @param channelId
     * @param merchantId
     * @return
     */
    String getMerchantKey(String channelId,String merchantId);


}
