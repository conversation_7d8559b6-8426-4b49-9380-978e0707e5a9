package com.eyuan.pay.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.mapper.PayTradeOrderMapper;
import com.eyuan.pay.service.PayTradeOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 支付
 *
 * <AUTHOR>
 * @date 2019-05-28 23:58:18
 */
@Service
@Slf4j
public class PayTradeOrderServiceImpl extends ServiceImpl<PayTradeOrderMapper, PayTradeOrder> implements PayTradeOrderService {

	@Override
	public List<PayTradeOrder> findByChannelOrderNo(String channelOrderNo) {
		log.info("根据三方支付单号查询支付记录, channelOrderNo:{}", channelOrderNo);
		Assert.notBlank(channelOrderNo, "参数错误，三方支付单号不存在");
		return this.baseMapper.selectList(new LambdaQueryWrapper<PayTradeOrder>().eq(PayTradeOrder::getChannelOrderNo, channelOrderNo));
	}

	@Override
	public PayTradeOrder getByChannelOrderNo(String channelOrderNo) {
		List<PayTradeOrder> payTradeOrders = this.findByChannelOrderNo(channelOrderNo);
		if (CollectionUtil.isNotEmpty(payTradeOrders)) {
			return payTradeOrders.get(0);
		}
		return null;
	}

	@Override
	public PayTradeOrder getByOrderId(String payOrderId) {
		return this.baseMapper.selectById(payOrderId);
	}
}
