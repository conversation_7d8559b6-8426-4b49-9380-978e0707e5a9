package com.eyuan.pay.service;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eyuan.pay.entity.PayGoodsOrder;

import java.util.Map;

/**
 * 商品
 *
 * <AUTHOR>
 * @date 2019-05-28 23:58:27
 */
public interface PayGoodsOrderService extends IService<PayGoodsOrder> {

	/**
	 * 购买商品
	 *
	 * @param goodsOrder goods
	 * @return
	 */
	Map<String, Object> buy(PayGoodsOrder goodsOrder);

	PayGoodsOrder getByPayOrderId(String payOrderId);

	PayGoodsOrder getByPayOrderIdAndValid(String payOrderId);
}
