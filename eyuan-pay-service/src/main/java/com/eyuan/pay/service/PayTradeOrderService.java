package com.eyuan.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eyuan.pay.entity.PayTradeOrder;

import java.util.List;

/**
 * 支付
 *
 * <AUTHOR>
 * @date 2019-05-28 23:58:18
 */
public interface PayTradeOrderService extends IService<PayTradeOrder> {

	List<PayTradeOrder> findByChannelOrderNo(String channelOrderNo);

	PayTradeOrder getByChannelOrderNo(String channelOrderNo);

	PayTradeOrder getByOrderId(String payOrderId);

}
