package com.eyuan.pay.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eyuan.pay.entity.PayTradeRecord;
import com.eyuan.pay.mapper.PayTradeRecordMapper;
import com.eyuan.pay.service.PayTradeRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易记录
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class PayTradeRecordServiceImpl extends ServiceImpl<PayTradeRecordMapper, PayTradeRecord> implements PayTradeRecordService {

    @Override
    public PayTradeRecord getByTradeNo(String tradeNo) {
        Assert.notNull(StringUtils.trimToNull(tradeNo), "参数错误，交易号不能为空");
        return this.baseMapper.getByTradeNo(tradeNo);
    }

    @Override
    public PayTradeRecord findByThirdTradeNo(String thirdTradeNo) {
        Assert.notNull(StringUtils.trimToNull(thirdTradeNo), "参数错误，三方交易号不能为空");
        return this.baseMapper.findByThirdTradeNo(thirdTradeNo);
    }

    @Override
    public List<PayTradeRecord> findByOrderNo(String orderNo) {
        Assert.notNull(StringUtils.trimToNull(orderNo), "参数错误，业务订单号不能为空");
        return this.baseMapper.findByOrderNo(orderNo);
    }
}