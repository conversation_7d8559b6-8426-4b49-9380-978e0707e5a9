package com.eyuan.pay.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dog.common.core.context.TenantHolder;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.config.HandlerLocator;
import com.eyuan.pay.entity.PayGoodsOrder;
import com.eyuan.pay.entity.PayTradeOrder;
import com.eyuan.pay.enums.AppPlatformType;
import com.eyuan.pay.enums.PayToolEnum;
import com.eyuan.pay.handler.PayOrderHandler;
import com.eyuan.pay.handler.RefundOrderHandler;
import com.eyuan.pay.request.OrderPayRequest;
import com.eyuan.pay.request.OrderRefundRequest;
import com.eyuan.pay.response.OrderRefundResponse;
import com.eyuan.pay.service.PayTradeOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 支付处理
 * @date 2020-08-04 11:38
 */
@Slf4j
@Service
@AllArgsConstructor
public class PayOrderFacade {

	private final HandlerLocator handlerLocator;
	private final HttpServletRequest httpRequest;
	private final PayTradeOrderService payTradeOrderService;

	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> payOrder(OrderPayRequest request) {
		log.info("[支付请求] request:{}", request);

		Assert.notBlank(request.getUserId(), "参数错误, 用户id不能为空");
		if (StrUtil.isAllBlank(request.getPlatformType())) {
			request.setPlatformType(AppPlatformType.B2C.value());
		}
		Assert.notBlank(request.getPlatformType(), "参数错误，平台类型不能为空");

		AppPlatformType.valid(request.getPlatformType());

		Integer payChannel = request.getPayChannel();
		PayToolEnum payToolEnum = PayToolEnum.valid(payChannel);
		String channel = payToolEnum.name();
		String ua = httpRequest.getHeader(HttpHeaders.USER_AGENT);
		log.info("[支付请求] {}:{}", HttpHeaders.USER_AGENT, ua);
		// 临时增加 start
//		ua = PayConstants.MICRO_MESSENGER;
		// 临时增加 end

		log.info("[支付请求] 支付方式:{}", channel);
		PayOrderHandler orderHandler = handlerLocator.getPayOrderHandler(payToolEnum.value());
		log.info("[支付请求] 支付handler:{}", orderHandler);

		PayGoodsOrder payGoodsOrder = BeanUtil.copyProperties(request, PayGoodsOrder.class);
		payGoodsOrder.setPlatformType(request.getPlatformType());
		payGoodsOrder.setBizOrderCode(request.getPayOrderId());
		Object params = orderHandler.handle(payGoodsOrder);

		Map<String, Object> result = new HashMap<>(4);
		result.put("channel", channel);
		result.put("goods", payGoodsOrder);
		result.put("params", params);
		log.info("返回 result:{}", JSONUtil.toJsonStr(result));
		return result;
	}

	public OrderRefundResponse refund(OrderRefundRequest request) {
		log.info("[退款请求] request:{}", request);
		OrderRefundResponse orderRefundResponse = doRefund(request);
		return orderRefundResponse;
	}

	private OrderRefundResponse doRefund(OrderRefundRequest request) {
		PayToolEnum payToolEnum = getPayChannel(request);
		log.info("[退款请求] 退款渠道:{}", payToolEnum);
		AssertUtil.notNull(payToolEnum, "退款渠道未找到");

		RefundOrderHandler refundOrderHandler = handlerLocator.getRefundOrderHandler(payToolEnum.payChanelValue());
		log.info("[退款请求] 退款处理器:{}", refundOrderHandler);
		AssertUtil.notNull(refundOrderHandler, "退款处理器未找到");


		OrderRefundResponse orderRefundResponse = refundOrderHandler.handle(request);
		log.info("[退款请求] result:{}", JSON.toJSONString(orderRefundResponse));
		return orderRefundResponse;
	}

	private PayToolEnum getPayChannel(OrderRefundRequest request) {
		PayTradeOrder payTradeOrder = payTradeOrderService.getByOrderId(request.getPayOrderId());
		Assert.notNull(payTradeOrder, "支付订单信息不存在");
		TenantHolder.setTenantId(payTradeOrder.getTenantId());
		String channelId = payTradeOrder.getChannelId();
		request.setPayChannelId(channelId);
		return PayToolEnum.valueOf(channelId);
	}
}
