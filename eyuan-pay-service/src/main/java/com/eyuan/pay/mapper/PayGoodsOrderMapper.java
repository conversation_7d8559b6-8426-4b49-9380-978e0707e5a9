package com.eyuan.pay.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eyuan.pay.entity.PayGoodsOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商品
 *
 * <AUTHOR>
 * @date 2019-05-28 23:58:27
 */
@Mapper
public interface PayGoodsOrderMapper extends BaseMapper<PayGoodsOrder> {

    @SqlParser(filter = true)
    PayGoodsOrder getByPayOrderId(@Param("payOrderId") String payOrderId);

}
