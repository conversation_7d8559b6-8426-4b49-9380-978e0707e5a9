package com.eyuan.pay.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eyuan.pay.entity.PayTradeOrder;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;

/**
 * 支付
 *
 * <AUTHOR>
 * @date 2019-05-28 23:58:18
 */
@Mapper
public interface PayTradeOrderMapper extends BaseMapper<PayTradeOrder> {

    @Override
    @SqlParser(filter = true)
    PayTradeOrder selectById(Serializable orderId);
}
