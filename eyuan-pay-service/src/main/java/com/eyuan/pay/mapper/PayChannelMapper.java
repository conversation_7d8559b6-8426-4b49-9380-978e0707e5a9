package com.eyuan.pay.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eyuan.pay.entity.PayChannel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 渠道
 *
 * <AUTHOR>
 * @date 2019-05-28 23:57:58
 */
@Mapper
public interface PayChannelMapper extends BaseMapper<PayChannel> {


	@Override
	@SqlParser(filter = true)
	List<PayChannel> selectList(@Param("ew") Wrapper<PayChannel> queryWrapper);

	@Override
	@SqlParser(filter = true)
	List<PayChannel> selectByMap(@Param("cm") Map<String, Object> columnMap);

}
