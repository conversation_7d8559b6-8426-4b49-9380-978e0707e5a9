package com.eyuan.pay.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eyuan.pay.entity.PayTradeRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交易记录
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
@Mapper
public interface PayTradeRecordMapper extends BaseMapper<PayTradeRecord> {

    /**
     * 根据交易号查询交易记录
     * @param tradeNo 交易号
     * @return 交易记录
     */
    @SqlParser(filter = true)
    PayTradeRecord getByTradeNo(@Param("tradeNo") String tradeNo);
    
    /**
     * 根据三方交易号查询交易记录
     * @param thirdTradeNo 三方交易号
     * @return 交易记录列表
     */
    @SqlParser(filter = true)
    PayTradeRecord findByThirdTradeNo(@Param("thirdTradeNo") String thirdTradeNo);
    
    /**
     * 根据业务订单号查询交易记录
     * @param orderNo 业务订单号
     * @return 交易记录列表
     */
    @SqlParser(filter = true)
    List<PayTradeRecord> findByOrderNo(@Param("orderNo") String orderNo);
}