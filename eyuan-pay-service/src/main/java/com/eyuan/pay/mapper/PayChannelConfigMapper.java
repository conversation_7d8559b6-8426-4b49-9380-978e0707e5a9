package com.eyuan.pay.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eyuan.pay.entity.PayChannelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 支付渠道配置
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@Mapper
public interface PayChannelConfigMapper extends BaseMapper<PayChannelConfig> {

    @Override
    @SqlParser(filter = true)
    List<PayChannelConfig> selectList(@Param("ew") Wrapper<PayChannelConfig> queryWrapper);

    @Override
    @SqlParser(filter = true)
    List<PayChannelConfig> selectByMap(@Param("cm") Map<String, Object> columnMap);
}
