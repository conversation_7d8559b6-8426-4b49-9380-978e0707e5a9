package com.eyuan.pay.settlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 结算请求参数
 * @Author: sunwh
 * @Date: 2025/5/6 11:23
 */
@Data
public class SettlementRequest {

    /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;
    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("业务定单号")
    private String orderNo;

    @ApiModelProperty("商户号")
    private String merchantNo;

    @ApiModelProperty("交易类型")
    private Integer traderType;

    @ApiModelProperty("银行账户")
    private SettlementBankDTO settlementBankDTO;

}
