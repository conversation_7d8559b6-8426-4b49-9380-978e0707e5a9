package com.eyuan.pay.settlement.api;

import com.dog.common.core.util.R;
import com.eyuan.pay.request.SettlementCreateRequest;
import com.eyuan.pay.request.WithdrawCreateRequest;
import com.eyuan.pay.response.SettlementCreateResponse;
import com.eyuan.pay.response.WithdrawCreateResponse;
import com.eyuan.pay.settlement.facade.SettlementFacade;
import com.eyuan.pay.settlement.facade.WithdrawFacade;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算
 * @Author: sunwh
 * @Date: 2025/5/22 17:29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/rest/pay/settlement/" )
public class SettlementController {
    private final SettlementFacade settlementFacade;
    private final WithdrawFacade withdrawFacade;

    @PostMapping("create")
    public R<SettlementCreateResponse> create(@RequestBody  SettlementCreateRequest settlementCreateRequest) {
        return R.ok(settlementFacade.createSettlement(settlementCreateRequest));
    }


    @PostMapping("withdraw")
    public R<WithdrawCreateResponse> withdraw(@RequestBody WithdrawCreateRequest withdrawCreateRequest) {
        return R.ok(withdrawFacade.createWithdraw(withdrawCreateRequest));
    }
}
