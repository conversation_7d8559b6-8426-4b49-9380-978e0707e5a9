package com.eyuan.pay.settlement.consumer;

import cn.hutool.json.JSONUtil;
import com.eyuan.pay.constant.PayMQConstants;
import com.eyuan.pay.request.SettlementCreateRequest;
import com.eyuan.pay.settlement.facade.SettlementFacade;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 新增结算明细MQ消费
 * @Author: sunwh
 * @Date: 2025/5/17 16:34
 */
@Component
@AllArgsConstructor
@Slf4j
public class SettlementCreateReceiver {
    private SettlementFacade settlementFacade;

    @RabbitListener(
            bindings = @QueueBinding(                    //数据是否持久化
                    value = @Queue(value = PayMQConstants.MQ_PAY_CREATE_SETTLEMENT_QUEUE,durable = "true"),
                    exchange = @Exchange(name = PayMQConstants.MQ_EXCHANGE,type = "topic"),
                    key=PayMQConstants.MQ_PAY_CREATE_SETTLEMENT_QUEUE_ROUTING_KEY
            )
    )
    @RabbitHandler
    public void onOrderMessage(@Payload SettlementCreateRequest settlementCreateRequest) {
        log.info("新增结算明细推送接收参数：{}", JSONUtil.toJsonStr(settlementCreateRequest));
//        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);

        /**
         *  取值为 false 时，表示通知 RabbitMQ 当前消息被确认
         *  如果为 true，则额外将比第一个参数指定的 delivery tag 小的消息一并确认
         */
//        channel.basicAck(deliveryTag,false);
        settlementFacade.createSettlement(settlementCreateRequest);
    }
}
