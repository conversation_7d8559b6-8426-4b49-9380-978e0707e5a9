package com.eyuan.pay.settlement.handler;

import cn.hutool.json.JSONUtil;
import com.dog.common.core.util.AssertUtil;
import com.dog.common.sequence.sequence.Sequence;
import com.eyuan.pay.enums.SettlementTypeEnum;
import com.eyuan.pay.helipay.client.HeliTransferClient;
import com.eyuan.pay.helipay.enums.BizTypeEnum;
import com.eyuan.pay.helipay.enums.FeeTypeEnum;
import com.eyuan.pay.helipay.request.HeliPayTransferRequest;
import com.eyuan.pay.helipay.response.HeliPayTransferResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.helipay.util.HttpUtils;
import com.eyuan.pay.response.SettlementResponse;
import com.eyuan.pay.settlement.dto.SettlementBankDTO;
import com.eyuan.pay.settlement.dto.SettlementRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 代付业务处理器
 * @Author: sunwh
 * @Date: 2025/5/6 15:50
 */
@Slf4j
@Service
public class TransferHandler extends AbstractSettlementHandler<HeliPayTransferRequest,HeliPayTransferResponse> {
    @Resource
    private HeliTransferClient heliTransferClient;
    @Resource
    private Sequence paySequence;

    @Override
    protected SettlementResponse buildSettlementResponse(HeliPayTransferResponse heliPayTransferResponse) {
        SettlementResponse settlementResponse = new SettlementResponse();
        settlementResponse.setTradeNo(heliPayTransferResponse.getRt5_orderId());
        settlementResponse.setThirdTradeNo(heliPayTransferResponse.getRt6_serialNumber());
        return settlementResponse;
    }

    @Override
    protected HeliPayTransferRequest buildRequestParams(SettlementRequest request) {
        AssertUtil.notBlank(request.getMerchantNo(),"商户号不能为空");
        SettlementBankDTO bankAccount = request.getSettlementBankDTO();
        //合利宝每比代付接口都要求交易号不能重复，这里每次发起交易生成交易号，处理完成返回
        String tradeNo = paySequence.nextNo();
        HeliPayTransferRequest heliPayTransferRequest = new HeliPayTransferRequest();
        heliPayTransferRequest.setP1_bizType(BizTypeEnum.Transfer.name());
        heliPayTransferRequest.setP2_orderId(tradeNo);
        heliPayTransferRequest.setP3_customerNumber(request.getMerchantNo());
        heliPayTransferRequest.setP4_amount(String.valueOf(request.getSettlementAmount()));
        heliPayTransferRequest.setP11_urgency("true");
        heliPayTransferRequest.setP10_feeType(FeeTypeEnum.PAYER.name());
        heliPayTransferRequest.setNotifyUrl(notifyUrl());
        heliPayTransferRequest.setP5_bankCode(bankAccount.getBankCode());
        heliPayTransferRequest.setP6_bankAccountNo(bankAccount.getAccountNo());
        heliPayTransferRequest.setP7_bankAccountName(bankAccount.getAccountName());
        heliPayTransferRequest.setP8_biz(bankAccount.getBankType());
        heliPayTransferRequest.setP9_bankUnionCode(bankAccount.getBankUnionCode());
        return heliPayTransferRequest;
    }

    @Override
    protected HeliPayTransferResponse doExecute(HeliPayTransferRequest heliPayTransferRequest) {
        Map<String, String> map = HeliSignatureUtils.convertRequestAndCreateSign(heliPayTransferRequest,
                HeliPayTransferRequest.NEED_SIGN_PARAMS, HeliPayTransferRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
//        String result = heliTransferClient
//                .transfer(payCommonProperties.getHeliPayConfig().getTransferUrl(), map);
//        AssertUtil.isTrue(JSONUtil.isTypeJSON(result),"代付接口返回错误："+result);
//        HeliPayTransferResponse heliPayTransferResponse = JSONUtil.toBean(result, HeliPayTransferResponse.class);
        // 转换为 Map<String, Object>（实际上不需要，这里只是演示）
        log.info("合利宝代付请求参数：{}",JSONUtil.toJsonStr(map));
        String result = HttpUtils.doExecute(payCommonProperties.getHeliPayConfig().getTransferUrl(), map);
        log.info("合利宝代付请求响应结果：{}",result);
        AssertUtil.isTrue(JSONUtil.isTypeJSON(result),"代付接口返回错误："+result);
        HeliPayTransferResponse heliPayTransferResponse = JSONUtil.toBean(result, HeliPayTransferResponse.class);
//        String result = Forest.post(payCommonProperties.getHeliPayConfig().getTransferUrl())
//                .contentFormUrlEncoded() // 指定请求体为表单格式
//                .addBody(map).execute(String.class);
//        log.info("代付请求响应结果：{}",result);
        return heliPayTransferResponse;
    }

    @Override
    protected void validate(SettlementRequest request) {
        super.validate(request);
        AssertUtil.notNull(request.getSettlementBankDTO(),"银行账户不能为空");
    }

    @Override
    public Integer type() {
        return SettlementTypeEnum.BANK_CARD.getValue();
    }

    @Override
    public String notifyUrl() {
        return payCommonProperties.getHeliPayConfig().getTransferNotifyUrl();
    }
}
