package com.eyuan.pay.settlement.controller;

import com.dog.common.core.util.AssertUtil;
import com.dog.common.core.util.R;
import com.eyuan.pay.settlement.entity.BankAccount;
import com.eyuan.pay.settlement.enums.BankEnum;
import com.eyuan.pay.settlement.service.BankAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账户信息Controller
 */
@Api(tags = "银行账户信息管理")
@RestController
@RequestMapping("/rest/front/settlement/bankAccount")
@RequiredArgsConstructor
public class BankAccountController {

    private final BankAccountService accountInfoService;

    @ApiOperation("新增银行账户信息")
    @PostMapping("save")
    public R save(@Validated @RequestBody BankAccount bankAccount) {
        BankEnum bankEnum = BankEnum.getByCode(bankAccount.getBankCode());
        AssertUtil.notNull(bankEnum, "不支持该银行编码");
        // 校验供应商是否已有账户信息
        BankAccount existAccount = accountInfoService.getBankAccount(bankAccount.getUserType(), bankAccount.getUserId(), bankAccount.getBankCode());
        if (existAccount != null) {
            return R.failed("该用户已存在银行账户信息");
        }
        accountInfoService.save(bankAccount);
        return R.ok();
    }

    @ApiOperation("更新账户信息")
    @PutMapping("update")
    public R update(@Validated @RequestBody BankAccount bankAccount) {
        BankEnum bankEnum = BankEnum.getByCode(bankAccount.getBankCode());
        AssertUtil.notNull(bankEnum, "不支持该银行编码");
        // 校验供应商是否已有其他账户信息
        BankAccount existAccount = accountInfoService.getBankAccount(bankAccount.getUserType(), bankAccount.getUserId(), bankAccount.getBankCode());
        if (existAccount != null && !existAccount.getId().equals(bankAccount.getId())) {
            return R.failed("该供应商已存在其他账户信息");
        }
        accountInfoService.updateById(bankAccount);
        return R.ok();
    }

    @ApiOperation("获取银行账户列表")
    @GetMapping("/queryBankAccountList")
    public R<List<BankAccount>> queryBankAccountList(@RequestParam String userType, @RequestParam String userId) {
        return R.ok(accountInfoService.queryBankAccountList(userType,userId));
    }

    /**
     * 删除银行账户
     * @param id id
     * @return R
     */
    @ApiOperation(value = "删除银行账户")
    @DeleteMapping("/{id}" )
    public R removeById(@PathVariable String id) {
        return R.ok(accountInfoService.removeById(id));
    }
} 