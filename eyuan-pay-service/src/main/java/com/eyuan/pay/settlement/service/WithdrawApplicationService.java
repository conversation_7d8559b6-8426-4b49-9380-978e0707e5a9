package com.eyuan.pay.settlement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eyuan.pay.settlement.entity.WithdrawApplication;

/**
 * 提现申请
 *
 * <AUTHOR>
 * @date 2024-05-15 10:30:00
 */
public interface WithdrawApplicationService extends IService<WithdrawApplication> {

    /**
     * 分页查询提现申请
     * @param page 分页对象
     * @param accountId 账户ID
     * @param accountType 账户类型
     * @return 分页结果
     */
    IPage<WithdrawApplication> pageWithdraws(IPage<WithdrawApplication> page, String accountId, Integer accountType);

    /**
     * 根据交易号查询提现申请
     * @param withdrawNo 提现订单号
     * @return 提现申请
     */
    WithdrawApplication getByWithdrawNo(String withdrawNo);

}
