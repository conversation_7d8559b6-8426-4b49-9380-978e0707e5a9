package com.eyuan.pay.settlement.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eyuan.pay.settlement.entity.SettlementDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 结算明细Mapper接口
 */
@Mapper
public interface SettlementDetailMapper extends BaseMapper<SettlementDetail> {

    /**
     * 查询待结算的明细列表
     * 使用@SqlParser(filter = true)注解去除租户过滤
     *
     * @param status 结算状态
     * @param settlementTime 结算时间
     * @return 待结算明细列表
     */
    @SqlParser(filter = true)
    List<SettlementDetail> findPendingSettlements(@Param("status") String status,
                                                 @Param("settlementTime") LocalDateTime settlementTime);
}
