package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 结算批次
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@Data
@TableName("settlement_batch")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "结算批次")
public class SettlementBatch extends Model<SettlementBatch> {
    private static final long serialVersionUID = 1L;

    /**
     * 批次号
     */
    @TableId
    private Long id;

    /**
     * 结算日期
     */
    private LocalDate settlementDate;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 结算总金额
     */
    private BigDecimal totalAmount;

    /**
     * 状态（pending/success/failed）
     */
    private String status;

    /**
     * 待结算时间
     */
    private LocalDateTime settlementTime;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private String deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;
} 