package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 银行账户信息实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bank_account")
@ApiModel(value = "银行账户")
public class BankAccount extends Model<BankAccount> {


    /**
     * 主键
     */
    @TableId
    private Long id;

    @ApiModelProperty("用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @ApiModelProperty("用户类型")
    @NotBlank(message = "用户类型不能为空")
    private String userType;

    @ApiModelProperty("账户名称")
    private String accountName;
    @ApiModelProperty("银行账号")
    @NotBlank(message = "银行账号不能为空")
    @Size(max = 32, message = "银行账号长度不能超过32个字符")
    private String accountNo;
    @ApiModelProperty("开户银行")
    @NotBlank(message = "开户银行不能为空")
    @Size(max = 64, message = "开户银行长度不能超过64个字符")
    private String bankName;

    @ApiModelProperty("支行名称")
    @Size(max = 128, message = "支行名称长度不能超过128个字符")
    private String bankBranch;

    @ApiModelProperty("银行编码")
    @NotBlank(message = "银行编码不能为空")
    @Size(max = 32, message = "银行编码长度不能超过32个字符")
    private String bankCode;

    @ApiModelProperty("业务类型（B2B-对公，B2C-对私）")
    @NotBlank(message = "业务类型不能为空")
    @Pattern(regexp = "^(B2B|B2C)$", message = "业务类型只能是B2B或B2C")
    private String bankType;

    @ApiModelProperty("银行联行号")
    @Size(max = 32, message = "银行联行号长度不能超过32个字符")
    private String bankUnionCode;

    @ApiModelProperty("开户省份")
    @Size(max = 32, message = "开户省份长度不能超过32个字符")
    private String bankProvince;

    @ApiModelProperty("开户城市")
    @Size(max = 32, message = "开户城市长度不能超过32个字符")
    private String bankCity;

    @ApiModelProperty("身份证号")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", message = "身份证号格式不正确")
    private String idCardNo;

    @ApiModelProperty("手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty("状态（T-启用，F-禁用）")
    @Pattern(regexp = "^(T|F)$", message = "状态只能是T或F")
    private String status;

    @ApiModelProperty("备注")
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private String deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 