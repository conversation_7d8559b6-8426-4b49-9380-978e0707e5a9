package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分账明细
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@Data
@TableName("split_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "分账明细")
public class SplitDetail extends Model<SplitDetail> {
    private static final long serialVersionUID = 1L;

    /**
     * 分账明细ID
     */
    @TableId
    private Long id;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 分账金额
     */
    private BigDecimal splitAmount;

    /**
     * 平台分成金额
     */
    private BigDecimal platformAmount;

    /**
     * 分账类型（pay/refund）
     */
    private String splitType;

    /**
     * 原分账明细ID（退款时用）
     */
    private Long originSplitId;

    /**
     * 分账状态（pending/success/failed）
     */
    private String splitStatus;

    /**
     * 分账时间
     */
    private LocalDateTime splitTime;

    /**
     * 分账模式
     */
    private String splitMode;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private String deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;
} 