package com.eyuan.pay.settlement.handler;

import com.dog.common.core.util.R;
import com.eyuan.pay.settlement.dto.SettlementRequest;

/**
 * 结算处理器
 * @Author: sunwh
 * @Date: 2025/5/6 11:29
 */
public interface SettlementHandler<Request,Response> {

    /**
     * 执行
     * @param request
     * @return
     */
    R execute(SettlementRequest request);


    /**
     * 结算类型
     * @return
     */
    Integer type();

    /**
     * 通知地址
     * @return
     */
    String notifyUrl();
}
