package com.eyuan.pay.settlement.producer;

import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONUtil;
import com.eyuan.pay.constant.PayMQConstants;
import com.eyuan.pay.request.SettlementNoticeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 结算审核通知发送者
 * <AUTHOR>
 * @date 2025/5/20
 */
@Component
@Slf4j
public class SettlementAuditNoticeSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送结算审核通知
     * @param request 结算审核通知请求
     */
    public void send(SettlementNoticeRequest request) {
        log.info("发送结算审核通知, 参数: {}", JSONUtil.toJsonStr(request));
        
        // 创建消息关联数据
        CorrelationData correlationData = new CorrelationData();
        correlationData.setId(UUID.fastUUID().toString());
        
        // 发送消息
        rabbitTemplate.convertAndSend(
                PayMQConstants.MQ_EXCHANGE,
                PayMQConstants.MQ_SETTLEMENT_AUDIT_NOTICE_ROUTING_KEY,
                request,
                correlationData
        );
    }
}