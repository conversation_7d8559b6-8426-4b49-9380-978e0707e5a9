package com.eyuan.pay.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eyuan.pay.settlement.entity.SettlementConfig;
import com.eyuan.pay.settlement.mapper.SettlementConfigMapper;
import com.eyuan.pay.settlement.service.SettlementConfigService;
import org.springframework.stereotype.Service;

/**
 * 结算配置Service实现类
 */
@Service
public class SettlementConfigServiceImpl extends ServiceImpl<SettlementConfigMapper, SettlementConfig> implements SettlementConfigService {
    @Override
    public SettlementConfig getBySupplierId(String supplierId) {
        LambdaQueryWrapper<SettlementConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SettlementConfig::getSupplierId, supplierId);
        return this.getOne(queryWrapper, false);
    }
} 