package com.eyuan.pay.settlement.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dog.common.core.context.TenantHolder;
import com.dog.common.core.context.UserHolder;
import com.dog.common.core.util.AssertUtil;
import com.dog.common.core.util.R;
import com.eyuan.pay.enums.*;
import com.eyuan.pay.request.SettlementNoticeRequest;
import com.eyuan.pay.request.WithdrawCreateRequest;
import com.eyuan.pay.response.SettlementResponse;
import com.eyuan.pay.response.WithdrawCreateResponse;
import com.eyuan.pay.settlement.dto.SettlementBankDTO;
import com.eyuan.pay.settlement.dto.SettlementRequest;
import com.eyuan.pay.settlement.dto.WithdrawAuditDTO;
import com.eyuan.pay.settlement.entity.BankAccount;
import com.eyuan.pay.settlement.entity.WithdrawApplication;
import com.eyuan.pay.settlement.enums.BankEnum;
import com.eyuan.pay.settlement.handler.SettlementHandleFactory;
import com.eyuan.pay.settlement.handler.SettlementHandler;
import com.eyuan.pay.settlement.producer.SettlementAuditNoticeSender;
import com.eyuan.pay.settlement.service.BankAccountService;
import com.eyuan.pay.settlement.service.WithdrawApplicationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现业务聚合
 * @Author: sunwh
 * @Date: 2025/6/6 14:47
 */
@Service
@AllArgsConstructor
@Slf4j
public class WithdrawFacade {
    private final WithdrawApplicationService withdrawApplicationService;
    private final BankAccountService bankAccountService;
    private final SettlementHandleFactory settlementHandleFactory;
    private final SettlementAuditNoticeSender settlementAuditNoticeSender;

    /**
     * 创建提现申请
     * @param request 提现申请请求
     * @return 提现申请响应
     */
    @Transactional(rollbackFor = Exception.class)
    public WithdrawCreateResponse createWithdraw(WithdrawCreateRequest request) {
        log.info("[提现] 创建提现申请, request:{}", request);
        // 参数校验
        AssertUtil.notBlank(request.getUserType(), "账户类型不能为空");
        AssertUtil.notBlank(request.getUserId(), "账户ID不能为空");
        AssertUtil.notNull(request.getAmount(), "提现金额不能为空");
        AssertUtil.isTrue(request.getAmount().compareTo(BigDecimal.ZERO) > 0, "提现金额必须大于0");
        AssertUtil.notNull(request.getWithdrawType(), "提现方式不能为空");
        AssertUtil.notNull(request.getWithdrawAccountId(), "提现账户ID不能为空");
        AssertUtil.notBlank(request.getWithdrawNo(), "提现单号不能为空");

        // 设置租户ID
        if (StrUtil.isNotBlank(request.getTenantId())) {
            TenantHolder.setTenantId(request.getTenantId());
        }
        SettlementTypeEnum withdrawTypeEnum = SettlementTypeEnum.getByValue(request.getWithdrawType());
        AssertUtil.notNull(withdrawTypeEnum, "不支持结算方式");
        String withdrawAccountInfo = null;
        if (SettlementTypeEnum.BANK_CARD.getValue().equals(request.getWithdrawType())) {
            AssertUtil.notBlank(request.getWithdrawAccountId(), "银行编码不能为空");
            BankEnum bankEnum = BankEnum.getByCode(request.getWithdrawAccountId());
            AssertUtil.notNull(bankEnum, "不支持该银行编码");

            BankAccount bankAccount = bankAccountService.getBankAccount(AccountUserTypeEnum.MEMBER.getCode(),
                    request.getUserId(), request.getWithdrawAccountId());
            AssertUtil.notNull(bankAccount, "银行账户不存在");
            SettlementBankDTO settlementBankDTO = BeanUtil.toBean(bankAccount, SettlementBankDTO.class);
            withdrawAccountInfo = JSONUtil.toJsonStr(settlementBankDTO);
        }

        // 创建提现申请
        WithdrawApplication withdraw = new WithdrawApplication();
        withdraw.setWithdrawNo(request.getWithdrawNo());
        withdraw.setUserType(request.getUserType());
        withdraw.setUserId(request.getUserId());
        withdraw.setAmount(request.getAmount());
        withdraw.setWithdrawType(request.getWithdrawType());
        withdraw.setPayerId(request.getPayerId());
        withdraw.setPayerType(request.getPayerType());
        withdraw.setStatus(WithdrawStatusEnum.PENDING.getValue());
        withdraw.setWithdrawAccountId(request.getWithdrawAccountId());// 审核中
        withdraw.setWithdrawAccountInfo(withdrawAccountInfo);

        // 保存提现申请
        withdrawApplicationService.save(withdraw);

        // 构建响应
        WithdrawCreateResponse response = new WithdrawCreateResponse();
        response.setId(withdraw.getId());
        response.setWithdrawNo(withdraw.getWithdrawNo());
        response.setUserType(withdraw.getUserType());
        response.setUserId(withdraw.getUserId());
        response.setAmount(withdraw.getAmount());
        response.setStatus(withdraw.getStatus());
        response.setCreateTime(withdraw.getCreateTime());
        return response;
    }

    /**
     * 审核提现申请
     * @param withdrawAuditDTO 提现审核请求
     * @return 提现审核响应
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditWithdraw(WithdrawAuditDTO withdrawAuditDTO) {
        // 参数校验
        AssertUtil.notBlank(withdrawAuditDTO.getWithdrawId(), "提现申请ID不能为空");
//        AssertUtil.notBlank(withdrawAuditDTO.getAuditUser(), "审核人不能为空");
        AssertUtil.notNull(withdrawAuditDTO.getStatus(), "审核状态不能为空");
        AssertUtil.isTrue(withdrawAuditDTO.getStatus().equals(WithdrawStatusEnum.APPROVED.getValue())
                || withdrawAuditDTO.getStatus().equals(WithdrawStatusEnum.REJECTED.getValue()),
                "审核状态只能是审核通过或审核拒绝");

        // 获取提现申请
        WithdrawApplication withdraw = withdrawApplicationService.getById(withdrawAuditDTO.getWithdrawId());
        AssertUtil.notNull(withdraw, "提现申请不存在");
        AssertUtil.isTrue(withdraw.getStatus().equals(WithdrawStatusEnum.PENDING.getValue()), 
                "只能审核状态为审核中的申请");

        // 更新提现申请状态
        withdraw.setStatus(withdrawAuditDTO.getStatus());
        String auditUser = StrUtil.isNotBlank(withdrawAuditDTO.getAuditUser()) ? withdrawAuditDTO.getAuditUser() : UserHolder.getName();
        withdraw.setAuditUser(auditUser);
        withdraw.setAuditTime(LocalDateTime.now());
        withdraw.setAuditRemark(withdrawAuditDTO.getAuditRemark());

        if (withdrawAuditDTO.getStatus().equals(WithdrawStatusEnum.REJECTED.getValue())) {
            withdraw.setStatus(WithdrawStatusEnum.REJECTED.getValue());
            this.sendWithdrawStatusNotice(withdraw, SettlementAuditStatusEnum.REJECTED.getValue(),SettlementStatusEnum.FAILED.getValue());
        } else if (withdrawAuditDTO.getStatus().equals(WithdrawStatusEnum.APPROVED.getValue())) {
            // 审核通过，状态改为打款中
            withdraw.setStatus(WithdrawStatusEnum.PROCESSING.getValue());
            
            // 调用支付系统进行打款处理
            log.info("提现申请审核通过，开始处理打款，提现单号：{}", withdraw.getWithdrawNo());
            SettlementResponse settlementResponse = this.processSettlement(withdraw);
            withdraw.setThirdTradeNo(settlementResponse.getThirdTradeNo());
        }

        // 保存更新
        withdrawApplicationService.updateById(withdraw);
    }

    /**
     * 更新结算状态并通知业务系统
     * @param tradeNo 交易号
     * @param status 结算状态
     * @param failReason 失败原因（如果有）
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSettlementStatusAndNotify(String tradeNo, Boolean status, String failReason) {
        log.info("更新结算状态并通知业务系统, tradeNo: {}, status: {}", tradeNo, status);
        // 1. 获取结算明细
        WithdrawApplication withdrawApplication = withdrawApplicationService.getByWithdrawNo(tradeNo);

        // 2. 更新结算状态
        Integer withdrawStatus = status ? WithdrawStatusEnum.SUCCESS.getValue() : WithdrawStatusEnum.FAILED.getValue();
        WithdrawApplication updateWithdrawApplication = new WithdrawApplication();
        updateWithdrawApplication.setId(withdrawApplication.getId());
        updateWithdrawApplication.setStatus(withdrawStatus);
        if (!status && failReason != null) {
            updateWithdrawApplication.setFailReason(failReason);
        }
        boolean updated = withdrawApplicationService.updateById(updateWithdrawApplication);

        // 3. 发送结算状态通知
        sendWithdrawStatusNotice(withdrawApplication,SettlementAuditStatusEnum.APPROVED.getValue(),
                status ? SettlementStatusEnum.SUCCESS.getValue() : SettlementStatusEnum.FAILED.getValue());
    }

    private SettlementResponse processSettlement(WithdrawApplication withdrawApplication) {
        SettlementRequest settlementRequest =  new SettlementRequest();
        settlementRequest.setSettlementAmount(withdrawApplication.getAmount());
        settlementRequest.setOrderNo(withdrawApplication.getWithdrawNo());
        settlementRequest.setTraderType(SettlementTradeTypeEnum.WITHDRAW.getValue());
        String withdrawAccountInfo = withdrawApplication.getWithdrawAccountInfo();
        SettlementBankDTO settlementBankDTO = JSONUtil.toBean(withdrawAccountInfo, SettlementBankDTO.class);
        settlementRequest.setSettlementBankDTO(settlementBankDTO);
        SettlementHandler settlementHandler = settlementHandleFactory.getSettlementHandler(withdrawApplication.getWithdrawType());
        R result = settlementHandler.execute(settlementRequest);
        AssertUtil.isTrue(result.success(),"转账到银行卡交易失败:"+result.getMsg());
        return (SettlementResponse)result.getData();
    }

    /**
     * 发送结算状态通知
     * @param withdrawApplication 结算明细
     */
    private void sendWithdrawStatusNotice(WithdrawApplication withdrawApplication,String auditStatus,String tradeStatus) {
        try {
            // 构建通知请求
            SettlementNoticeRequest noticeRequest = SettlementNoticeRequest.builder()
                    .orderNo(withdrawApplication.getWithdrawNo())
                    .thirdOrderNo(withdrawApplication.getThirdTradeNo())
                    .settlementAmount(withdrawApplication.getAmount())
                    .settlementType(withdrawApplication.getWithdrawType())
                    .auditStatus(auditStatus)
                    .tradeStatus(tradeStatus)
                    .auditTime(withdrawApplication.getAuditTime())
                    .auditRemark(withdrawApplication.getAuditRemark())
                    .failReason(withdrawApplication.getFailReason())
                    .tenantId(withdrawApplication.getTenantId())
                    .tradeType(SettlementTradeTypeEnum.WITHDRAW.getValue())
                    .userType(withdrawApplication.getUserType())
                    .userId(withdrawApplication.getUserId())
                    .build();

            // 发送通知
            settlementAuditNoticeSender.send(noticeRequest);
            log.info("提现状态通知发送成功, withdrawNo: {}, status: {}", withdrawApplication.getWithdrawNo(),tradeStatus);
        } catch (Exception e) {
            log.error("发送提现状态通知异常, withdrawNo: {}", withdrawApplication.getWithdrawNo(), e);
            // 这里可以选择抛出异常或者只记录日志
            // 如果是关键通知，建议抛出异常以便事务回滚
            // 如果是非关键通知，可以只记录日志
        }
    }
}
