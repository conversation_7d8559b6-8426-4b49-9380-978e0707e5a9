package com.eyuan.pay.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eyuan.pay.settlement.entity.BankAccount;

import java.util.List;

/**
 * 账户信息Service接口
 */
public interface BankAccountService extends IService<BankAccount> {
    /**
     * 查询指定银行编码的账户信息
     *
     * @param userType 用户类型
     * @param userId 用户id
     * @param bankCode 银行编码
     * @return 账户信息列表
     */
    BankAccount getBankAccount(String userType, String userId, String bankCode);

    /**
     * 查询指定用户的账户信息列表
     * @param userType
     * @param userId
     * @return
     */
    List<BankAccount> queryBankAccountList(String userType, String userId);
}