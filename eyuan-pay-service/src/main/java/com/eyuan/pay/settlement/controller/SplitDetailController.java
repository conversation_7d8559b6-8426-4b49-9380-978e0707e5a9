package com.eyuan.pay.settlement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.util.R;
import com.eyuan.pay.settlement.entity.SplitDetail;
import com.eyuan.pay.settlement.service.SplitDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 分账明细Controller
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@RestController
@RequestMapping("/split/detail")
@Api(tags = "分账明细")
public class SplitDetailController {

    @Autowired
    private SplitDetailService splitDetailService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param splitDetail 分账明细
     * @return
     */
    @GetMapping("/page")
    public R pageList(Page page, SplitDetail splitDetail) {
        return R.ok(splitDetailService.page(page, Wrappers.query(splitDetail)));
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询分账明细")
    public R getById(@PathVariable Long id) {
        splitDetailService.getById(id);
        return R.ok();
    }
}