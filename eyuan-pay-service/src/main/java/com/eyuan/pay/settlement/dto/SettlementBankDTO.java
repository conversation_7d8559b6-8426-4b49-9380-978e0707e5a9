package com.eyuan.pay.settlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 银行账户DTO
 * @Author: sunwh
 * @Date: 2025/6/6 15:29
 */
@Data
public class SettlementBankDTO {
    @ApiModelProperty("账户名称")
    private String accountName;
    @ApiModelProperty("银行账号")
    private String accountNo;
    @ApiModelProperty("开户银行")
    private String bankName;

    @ApiModelProperty("支行名称")
    private String bankBranch;

    @ApiModelProperty("银行编码")
    private String bankCode;

    @ApiModelProperty("业务类型（B2B-对公，B2C-对私）")
    private String bankType;

    @ApiModelProperty("银行联行号")
    private String bankUnionCode;
}
