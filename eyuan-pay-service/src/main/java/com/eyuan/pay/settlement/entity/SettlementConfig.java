package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("settlement_config")
@ApiModel("结算配置")
public class SettlementConfig extends Model<SettlementConfig> {

    @TableId
    private Long id;

    @ApiModelProperty("供应商ID")
    @NotBlank(message = "供应商ID不能为空")
    private String supplierId;

    @ApiModelProperty("结算到哪（1:银行卡,2:微信,3:支付宝,5:余额）")
    private Integer settleTo;

    @ApiModelProperty("是否审核（T-是，F-否）")
    private String needAudit;

    @ApiModelProperty("收取手续费：PAYER-付款方,RECEIVER-收款方")
    private String feeType;

    @ApiModelProperty("商户号")
    private String merchantNo;

    @ApiModelProperty("订单核销后N天结算")
    private Integer settlementAfterDays;

    @ApiModelProperty("备注")
    private String remark;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private String deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;
} 