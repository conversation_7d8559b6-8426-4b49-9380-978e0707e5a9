package com.eyuan.pay.settlement.handler;

import com.dog.common.core.util.AssertUtil;
import com.dog.common.sequence.sequence.Sequence;
import com.eyuan.pay.enums.SettlementTypeEnum;
import com.eyuan.pay.helipay.client.HeliTransferClient;
import com.eyuan.pay.helipay.enums.BizTypeEnum;
import com.eyuan.pay.helipay.request.HeliPaySettlementRequest;
import com.eyuan.pay.helipay.response.HeliPaySettlementResponse;
import com.eyuan.pay.helipay.util.HeliSignatureUtils;
import com.eyuan.pay.response.SettlementResponse;
import com.eyuan.pay.settlement.dto.SettlementRequest;
import com.eyuan.pay.settlement.entity.SettlementConfig;
import com.eyuan.pay.settlement.service.SettlementConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 代付业务处理器
 * @Author: sunwh
 * @Date: 2025/5/6 15:50
 */
@Slf4j
@Service
public class MerchantSettlementHandler extends AbstractSettlementHandler<HeliPaySettlementRequest, HeliPaySettlementResponse> {
    @Resource
    private HeliTransferClient heliTransferClient;
    @Resource
    private SettlementConfigService settlementConfigService;
    @Resource
    private Sequence paySequence;

    @Override
    public Integer type() {
        return SettlementTypeEnum.SETTLEMENT.getValue();
    }

    @Override
    public String notifyUrl() {
        return payCommonProperties.getHeliPayConfig().getTransferNotifyUrl();
    }

    @Override
    protected SettlementResponse buildSettlementResponse(HeliPaySettlementResponse heliPaySettlementResponse) {
        SettlementResponse settlementResponse = new SettlementResponse();
        settlementResponse.setTradeNo(heliPaySettlementResponse.getRt5_orderId());
        return settlementResponse;
    }

    @Override
    protected HeliPaySettlementRequest buildRequestParams(SettlementRequest request) {
        SettlementConfig settlementConfig = settlementConfigService.getBySupplierId(request.getUserId());
        AssertUtil.notNull(settlementConfig, "未配置结算信息");
        AssertUtil.notBlank(settlementConfig.getMerchantNo(), "未配置商户号");

        String tradeNo = paySequence.nextNo();
        HeliPaySettlementRequest heliPaySettlementRequest = new HeliPaySettlementRequest();
        heliPaySettlementRequest.setP1_bizType(BizTypeEnum.MerchantSettlement.name());
        heliPaySettlementRequest.setP2_orderId(tradeNo);
        heliPaySettlementRequest.setP3_customerNumber(settlementConfig.getMerchantNo());
        heliPaySettlementRequest.setP4_amount(String.valueOf(request.getSettlementAmount()));
        heliPaySettlementRequest.setP6_notifyUrl(notifyUrl());
        return heliPaySettlementRequest;
    }

    @Override
    protected HeliPaySettlementResponse doExecute(HeliPaySettlementRequest heliPaySettlementRequest) {
        Map<String, String> map = HeliSignatureUtils.convertRequestAndCreateSign(heliPaySettlementRequest,
                HeliPaySettlementRequest.NEED_SIGN_PARAMS, HeliPaySettlementRequest.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
        HeliPaySettlementResponse heliPaySettlementResponse = heliTransferClient
                .settle(payCommonProperties.getHeliPayConfig().getTransferUrl(), map);
        return heliPaySettlementResponse;
    }
}
