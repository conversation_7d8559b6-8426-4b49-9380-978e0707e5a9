package com.eyuan.pay.settlement.handler;

import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.enums.SettlementTypeEnum;
import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by wenh on 2022/7/5 17:11
 */
@Component
public class SettlementHandleFactory implements ApplicationContextAware {

    /**
     * 结算接口处理类容器
     * key :结算类型
     * value：结算接口处理类
     */
    private Map<Integer, SettlementHandler> settlementHandlerMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, SettlementHandler> beansOfType = applicationContext.getBeansOfType(SettlementHandler.class);
        beansOfType.values().forEach(settlementHandler -> {
            settlementHandlerMap.put(settlementHandler.type(), settlementHandler);
        });
    }

    public SettlementHandler getSettlementHandler(Integer settlementType){
        SettlementTypeEnum settlementTypeEnum = SettlementTypeEnum.getByValue(settlementType);
        AssertUtil.notNull(settlementTypeEnum,"未定义该接口枚举值");
        SettlementHandler settlementHandler = settlementHandlerMap.get(settlementTypeEnum.getValue());
        AssertUtil.notNull(settlementHandler,"未定义该接口处理方法");
        return settlementHandler;
    }

}
