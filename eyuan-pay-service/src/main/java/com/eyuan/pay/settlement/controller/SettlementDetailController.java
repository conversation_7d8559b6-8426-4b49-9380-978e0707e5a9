package com.eyuan.pay.settlement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.util.R;
import com.eyuan.pay.settlement.entity.SettlementDetail;
import com.eyuan.pay.settlement.service.SettlementDetailService;
import com.eyuan.pay.settlement.dto.SettlementAuditDTO;
import com.eyuan.pay.settlement.facade.SettlementFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 结算明细Controller
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@RestController
@RequestMapping("/settlement/detail")
@Api(tags = "结算明细")
@RequiredArgsConstructor
public class SettlementDetailController {

    private final SettlementDetailService settlementDetailService;
    private final SettlementFacade settlementFacade;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param settlementDetail 结算明细
     * @return
     */
    @GetMapping("/page")
    public R pageList(Page page, SettlementDetail settlementDetail) {
        return R.ok(settlementDetailService.page(page, Wrappers.query(settlementDetail)));
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询结算明细")
    public R getById(@PathVariable Long id) {
        return R.ok(settlementDetailService.getById(id));
    }

    @ApiOperation("审核结算明细")
    @PostMapping("/audit")
    public R audit(@Validated @RequestBody SettlementAuditDTO dto) {
        settlementFacade.auditSettlement(dto);
        return R.ok();
    }
} 