package com.eyuan.pay.settlement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.util.R;
import com.eyuan.pay.settlement.entity.SplitLog;
import com.eyuan.pay.settlement.service.SplitLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 分账操作日志Controller
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@RestController
@RequestMapping("/split/log")
@Api(tags = "分账操作日志")
public class SplitLogController {

    @Autowired
    private SplitLogService splitLogService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param splitLog 分账操作日志
     * @return
     */
    @GetMapping("/page")
    public R pageList(Page page, SplitLog splitLog) {
        return R.ok(splitLogService.page(page, Wrappers.query(splitLog)));
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询分账日志")
    public R getById(@PathVariable Long id) {
        splitLogService.getById(id);
        return R.ok();
    }
} 