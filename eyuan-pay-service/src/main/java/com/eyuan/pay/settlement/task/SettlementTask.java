package com.eyuan.pay.settlement.task;

import com.eyuan.pay.settlement.facade.SettlementFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 结算定时任务
 */
@Slf4j
@Component
public class SettlementTask {

    @Autowired
    private SettlementFacade settlementFacade;

    /**
     * 每分钟执行一次扫描待结算数据
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void scanPendingSettlements() {
        log.info("开始执行定时任务：扫描待结算数据");
        try {
            settlementFacade.scanPendingSettlements();
        } catch (Exception e) {
            log.error("扫描待结算数据异常", e);
        }
        log.info("结束执行定时任务：扫描待结算数据");
    }
} 