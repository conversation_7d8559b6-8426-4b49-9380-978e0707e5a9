package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dog.common.mybatis.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现申请
 *
 * <AUTHOR>
 * @date 2024-05-15 10:30:00
 */
@Data
@TableName("withdraw_application")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "提现申请")
public class WithdrawApplication extends BaseEntity<WithdrawApplication> {

    private static final long serialVersionUID = 1L;
                
    /**
     * 提现单号
     */
    @ApiModelProperty(value="提现单号")
    private String withdrawNo;

    /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;
            
    /**
     * 提现金额
     */
    @ApiModelProperty(value="提现金额")
    private BigDecimal amount;
    
    /**
     * 提现方式(1:银行卡,2:微信,3:支付宝,5:余额)
     */
    @ApiModelProperty(value="提现方式(1:银行卡,2:微信,3:支付宝,5:余额)")
    private Integer withdrawType;
            
    /**
     * 提现账户ID
     */
    @ApiModelProperty(value="提现账户ID")
    private String withdrawAccountId;


    /**
     * 提现账户信息
     */
    @ApiModelProperty(value="提现账户信息")
    private String withdrawAccountInfo;
            
    /**
     * 提现状态(0:审核中,1:审核通过,2:审核拒绝,3:打款中,4:打款成功,5:打款失败)
     */
    @ApiModelProperty(value="提现状态(0:审核中,1:审核通过,2:审核拒绝,3:打款中,4:打款成功,5:打款失败)")
    private Integer status;
    
    /**
     * 付款方ID
     */
    @ApiModelProperty(value="付款方ID")
    private String payerId;
    
    /**
     * 付款方类型(1:平台,2:商家,3:其他)
     */
    @ApiModelProperty(value="付款方类型(1:平台,2:商家,3:其他)")
    private Integer payerType;

    @ApiModelProperty("审核人")
    private String auditUser;
            
    /**
     * 审核时间
     */
    @ApiModelProperty(value="审核时间")
    private LocalDateTime auditTime;
            
    /**
     * 审核备注
     */
    @ApiModelProperty(value="审核备注")
    private String auditRemark;
            
    /**
     * 打款时间
     */
    @ApiModelProperty(value="打款时间")
    private LocalDateTime paymentTime;
            
    /**
     * 打款备注
     */
    @ApiModelProperty(value="打款备注")
    private String paymentRemark;

    @ApiModelProperty("三方交易号")
    private String thirdTradeNo;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String failReason;

    /**
     * 租户ID
     */
    private String tenantId;

}
