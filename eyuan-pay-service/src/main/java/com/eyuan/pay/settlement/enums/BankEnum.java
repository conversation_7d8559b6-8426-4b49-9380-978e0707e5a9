package com.eyuan.pay.settlement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 银行信息枚举
 * <AUTHOR>
 * @since 2023-07-10
 */
@AllArgsConstructor
@Getter
public enum BankEnum {

    ICBC("ICBC", "中国工商银行"),
    ABC("ABC", "中国农业银行"),
    CMBCHINA("CMBCHINA", "招商银行"),
    CCB("CCB", "中国建设银行"),
    BOCO("BOCO", "交通银行"),
    BOC("BOC", "中国银行"),
    CMBC("CMBC", "中国民生银行"),
    CGB("CGB", "广发银行"),
    HXB("HXB", "华夏银行"),
    POST("POST", "中国邮政储蓄银行"),
    ECITIC("ECITIC", "中信银行"),
    CEB("CEB", "中国光大银行"),
    PINGAN("PINGAN", "平安银行"),
    CIB("CIB", "兴业银行"),
    SPDB("SPDB", "浦发银行"),
    BCCB("BCCB", "北京银行"),
    BON("BON", "南京银行"),
    NBCB("NBCB", "宁波银行"),
    BEA("BEA", "东亚银行"),
    SRCB("SRCB", "上海农商银行"),
    SHB("SHB", "上海银行"),
    CZB("CZB", "浙商银行"),
    TCCB("TCCB", "天津银行"),
    HSBANK("HSBANK", "徽商银行"),
    HFBANK("HFBANK", "恒丰银行"),
    CBHB("CBHB", "渤海银行"),
    JSB("JSB", "江苏银行"),
    CITI("CITI", "花旗银行"),
    THX("THX", "贵阳银行"),
    HANGSENGBANK("HANGSENGBANK", "恒生银行"),
    GDNYBANK("GDNYBANK", "南粤银行"),
    LZBANK("LZBANK", "兰州银行");

    private final String code;
    private final String name;

    /**
     * 根据银行代码获取银行枚举
     *
     * @param code 银行代码
     * @return 银行枚举
     */
    public static BankEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(bank -> bank.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据银行名称获取银行枚举
     *
     * @param name 银行名称
     * @return 银行枚举
     */
    public static BankEnum getByName(String name) {
        return Arrays.stream(values())
                .filter(bank -> bank.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查银行代码是否有效
     *
     * @param code 银行代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}