package com.eyuan.pay.settlement.handler;

import cn.hutool.json.JSONUtil;
import com.dog.common.core.util.AssertUtil;
import com.dog.common.core.util.R;
import com.eyuan.pay.config.PayCommonProperties;
import com.eyuan.pay.entity.PayChannelConfig;
import com.eyuan.pay.entity.PayTradeRecord;
import com.eyuan.pay.enums.PayChannelEnum;
import com.eyuan.pay.enums.PayTradeStatusEnum;
import com.eyuan.pay.service.PayChannelConfigService;
import com.eyuan.pay.service.PayTradeRecordService;
import com.eyuan.pay.settlement.dto.SettlementRequest;
import com.eyuan.pay.response.SettlementResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 结算抽象处理
 * @Author: sunwh
 * @Date: 2025/5/6 11:31
 */
@Slf4j
public abstract class AbstractSettlementHandler<Request,Response> implements SettlementHandler<Request,Response> {

    @Autowired
    protected PayCommonProperties payCommonProperties;
    @Autowired
    private PayChannelConfigService payChannelConfigService;
    @Autowired
    private PayTradeRecordService payTradeRecordService;

    @Override
    public R execute(SettlementRequest request) {
        SettlementResponse settlementResponse = null;
        Request requestParams = null;
        Response response = null;
        try {
            //校验参数
            validate(request);
            //组装结算账户信息
            buildSettlementAccount(request);
            //组装请求参数
            requestParams = buildRequestParams(request);
            //执行业务逻辑
            response = doExecute(requestParams);
            //组装响应参数
            settlementResponse = buildSettlementResponse(response);
            //生成交易记录
            savePayTradeRecord(request, settlementResponse,requestParams,response, PayTradeStatusEnum.PROCESSING.getValue());
            return R.ok(settlementResponse);
        } catch (Exception e) {
            log.error("结算:{}，出现异常：{}",type(), e);
            savePayTradeRecord(request, settlementResponse,requestParams,response, PayTradeStatusEnum.FAIL.getValue());
            return R.failed(e.getMessage());
        }
    }

    protected abstract SettlementResponse buildSettlementResponse(Response response);

    protected abstract Request buildRequestParams(SettlementRequest request);

    private void savePayTradeRecord(SettlementRequest request, SettlementResponse settlementResponse, Request requestParams, Response responseParams, String status) {
        PayTradeRecord payTradeRecord = new PayTradeRecord();
        payTradeRecord.setOrderNo(request.getOrderNo());
        payTradeRecord.setTradeType(request.getTraderType());
        payTradeRecord.setTradeNo(settlementResponse.getTradeNo());
        payTradeRecord.setThirdTradeNo(settlementResponse.getThirdTradeNo());
        payTradeRecord.setRequest(JSONUtil.toJsonStr(requestParams));
        payTradeRecord.setResponse(JSONUtil.toJsonStr(responseParams));
        payTradeRecord.setStatus(status);
        payTradeRecordService.save(payTradeRecord);
    }

    private void buildSettlementAccount(SettlementRequest request) {
        //查询结算商户号
        PayChannelConfig payChannelConfig =  getPayChannelConfig(request);
        request.setMerchantNo(payChannelConfig.getMerchantId());
    }

    private PayChannelConfig getPayChannelConfig(SettlementRequest request) {
        return payChannelConfigService.getByChannelId(getChannelId());
    }

    private String getChannelId() {
        return PayChannelEnum.HELIPAY.getValue();
    }

    protected abstract Response doExecute(Request request);

    protected void validate(SettlementRequest request) {
        AssertUtil.notNull(request.getSettlementAmount(),"结算金额不能为空");
    }

}
