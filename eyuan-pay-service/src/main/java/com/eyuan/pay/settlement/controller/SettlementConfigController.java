package com.eyuan.pay.settlement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.util.R;
import com.eyuan.pay.settlement.entity.SettlementConfig;
import com.eyuan.pay.settlement.service.SettlementConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 结算配置Controller
 */
@Api(tags = "结算配置管理")
@RestController
@RequestMapping("/settlement/config")
@RequiredArgsConstructor
public class SettlementConfigController {

    private final SettlementConfigService settlementConfigService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param settlementConfig 结算配置
     * @return
     */
    @GetMapping("/page")
    public R pageList(Page page, SettlementConfig settlementConfig) {
        return R.ok(settlementConfigService.page(page, Wrappers.query(settlementConfig)));
    }

    @ApiOperation("新增结算配置")
    @PostMapping("save")
    public R save(@Validated @RequestBody SettlementConfig config) {
        // 校验供应商是否已有结算配置
        SettlementConfig exist = settlementConfigService.getBySupplierId(config.getSupplierId());
        if (exist != null) {
            return R.failed("该供应商已存在结算配置");
        }
        settlementConfigService.save(config);
        return R.ok();
    }

    @ApiOperation("修改结算配置")
    @PutMapping("update")
    public R update(@Validated @RequestBody SettlementConfig config) {
        // 校验供应商是否已有其他结算配置
        SettlementConfig exist = settlementConfigService.getBySupplierId(config.getSupplierId());
        if (exist != null && !exist.getId().equals(config.getId())) {
            return R.failed("该供应商已存在其他结算配置");
        }
        settlementConfigService.updateById(config);
        return R.ok();
    }

    @ApiOperation("获取结算配置")
    @GetMapping("/{supplierId}")
    public R<SettlementConfig> get(@PathVariable String supplierId) {
        return R.ok(settlementConfigService.getBySupplierId(supplierId));
    }

    /**
     * 删除结算配置
     * @param id id
     * @return R
     */
    @ApiOperation(value = "删除结算配置")
    @DeleteMapping("/{id}" )
    public R removeById(@PathVariable String id) {
        return R.ok(settlementConfigService.removeById(id));
    }

} 