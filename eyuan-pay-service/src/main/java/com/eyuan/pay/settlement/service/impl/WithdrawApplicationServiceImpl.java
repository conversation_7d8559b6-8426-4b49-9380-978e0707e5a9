package com.eyuan.pay.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eyuan.pay.settlement.entity.WithdrawApplication;
import com.eyuan.pay.settlement.mapper.WithdrawApplicationMapper;
import com.eyuan.pay.settlement.service.WithdrawApplicationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 提现申请
 *
 * <AUTHOR>
 * @date 2024-05-15 10:30:00
 */
@Slf4j
@Service
@AllArgsConstructor
public class WithdrawApplicationServiceImpl extends ServiceImpl<WithdrawApplicationMapper, WithdrawApplication> implements WithdrawApplicationService {

    @Override
    public IPage<WithdrawApplication> pageWithdraws(IPage<WithdrawApplication> page, String accountId, Integer accountType) {
        return this.baseMapper.pageWithdraws(page, accountId, accountType);
    }

    @Override
    public WithdrawApplication getByWithdrawNo(String withdrawNo) {
        return this.baseMapper.selectOne(new LambdaQueryWrapper<WithdrawApplication>().eq(WithdrawApplication::getWithdrawNo, withdrawNo));
    }
}
