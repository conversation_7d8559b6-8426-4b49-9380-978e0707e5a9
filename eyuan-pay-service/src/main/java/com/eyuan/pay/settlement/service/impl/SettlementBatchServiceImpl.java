package com.eyuan.pay.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eyuan.pay.settlement.entity.SettlementBatch;
import com.eyuan.pay.settlement.mapper.SettlementBatchMapper;
import com.eyuan.pay.settlement.service.SettlementBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 结算批次服务实现类
 */
@Slf4j
@Service
public class SettlementBatchServiceImpl extends ServiceImpl<SettlementBatchMapper, SettlementBatch> implements SettlementBatchService {
    @Override
    public void scanPendingSettlements() {
        log.info("开始扫描待结算数据");
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<SettlementBatch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SettlementBatch::getStatus, "pending")
                .le(SettlementBatch::getSettlementTime, now);
        this.list(queryWrapper).forEach(this::processSettlement);
        log.info("扫描待结算数据结束");
    }

    private void processSettlement(SettlementBatch batch) {
        log.info("处理结算批次: {}", batch.getId());
        // TODO: 实现结算逻辑
    }
} 