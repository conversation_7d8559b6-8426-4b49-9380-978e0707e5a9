package com.eyuan.pay.settlement.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 提现审核请求
 * <AUTHOR>
 * @date 2024-05-15
 */
@Data
@ApiModel("提现审核请求")
public class WithdrawAuditDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提现申请ID
     */
    @ApiModelProperty(value="提现申请ID", required=true)
    private String withdrawId;

    /**
     * 审核人
     */
    @ApiModelProperty(value="审核人", required=true)
    private String auditUser;

    /**
     * 审核状态(1:审核通过,2:审核拒绝)
     */
    @ApiModelProperty(value="审核状态(1:审核通过,2:审核拒绝)", required=true)
    private Integer status;

    /**
     * 审核备注
     */
    @ApiModelProperty(value="审核备注")
    private String auditRemark;
}