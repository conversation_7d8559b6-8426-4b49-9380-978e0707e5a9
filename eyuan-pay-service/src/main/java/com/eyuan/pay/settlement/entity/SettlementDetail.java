package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dog.common.mybatis.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算明细
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@Data
@TableName("settlement_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "结算明细")
public class SettlementDetail extends BaseEntity<SettlementDetail> {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("供应商ID")
    private String supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("分销商ID")
    private String distributorId;

    /**
     * 收款账户
     */
    @ApiModelProperty("收款账户")
    private String accountNo;

    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("订单号")
    private String orderNo;
    /**
     * 订单类型
     */
    @ApiModelProperty(value="订单类型")
    private String orderType;

    @ApiModelProperty("交易号")
    private String tradeNo;
    @ApiModelProperty("三方交易号")
    private String thirdTradeNo;

    @ApiModelProperty("结算类型（1:银行卡,2:微信,3:支付宝,5:余额）")
    private Integer type;

    @ApiModelProperty("待结算时间")
    private LocalDateTime settlementTime;

    @ApiModelProperty("结算时间")
    private LocalDateTime settledTime;

    /**
     * 状态（pending/success/failed）
     */
    @ApiModelProperty("结算状态（pending-待结算，processing-结算中，success-结算成功，failed-结算失败）")
    private String status;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String failReason;

    @ApiModelProperty("审核状态（PENDING-待审核，APPROVED-审核通过，REJECTED-审核拒绝）")
    private String auditStatus;

    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty("审核人")
    private String auditUser;

    @ApiModelProperty("审核备注")
    private String auditRemark;
    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;
} 