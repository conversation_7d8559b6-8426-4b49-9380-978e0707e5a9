package com.eyuan.pay.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dog.common.core.annotation.EnumBool;
import com.eyuan.pay.settlement.entity.BankAccount;
import com.eyuan.pay.settlement.mapper.BankAccountMapper;
import com.eyuan.pay.settlement.service.BankAccountService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 账户信息Service实现类
 */
@Service
public class BankAccountServiceImpl extends ServiceImpl<BankAccountMapper, BankAccount> implements BankAccountService {

    @Override
    public BankAccount getBankAccount(String userType, String userId, String bankCode) {
        LambdaQueryWrapper<BankAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BankAccount::getUserType, userType)
                .eq(BankAccount::getUserId, userId)
                .eq(BankAccount::getBankCode, bankCode);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<BankAccount> queryBankAccountList(String userType, String userId) {
        LambdaQueryWrapper<BankAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BankAccount::getUserType, userType)
                .eq(BankAccount::getUserId, userId)
                .eq(BankAccount::getStatus, EnumBool.TRUE.getValue());
        return this.list(queryWrapper);
    }
}