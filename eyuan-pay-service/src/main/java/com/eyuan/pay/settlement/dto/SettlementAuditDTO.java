package com.eyuan.pay.settlement.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("结算审核参数")
public class SettlementAuditDTO {
    @ApiModelProperty("结算明细ID")
    @NotNull(message = "结算明细ID不能为空")
    private Long id;

    @ApiModelProperty("审核状态(APPROVED/REJECTED)")
    @NotBlank(message = "审核状态不能为空")
    private String auditStatus;

    @ApiModelProperty("审核人")
    private String user;

    @ApiModelProperty("审核备注")
    private String remark;
} 