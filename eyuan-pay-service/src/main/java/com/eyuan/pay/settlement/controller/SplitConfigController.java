package com.eyuan.pay.settlement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.util.R;
import com.eyuan.pay.settlement.entity.SplitConfig;
import com.eyuan.pay.settlement.service.SplitConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商分账配置Controller
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@RestController
@RequestMapping("/split/config")
@Api(tags = "供应商分账配置")
public class SplitConfigController {

    @Autowired
    private SplitConfigService splitConfigService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param splitConfig 分账配置
     * @return
     */
    @GetMapping("/page")
    public R pageList(Page page, SplitConfig splitConfig) {
        return R.ok(splitConfigService.page(page, Wrappers.query(splitConfig)));
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询分账配置")
    public R getById(@PathVariable Long id) {
        return R.ok(splitConfigService.getById(id));
    }

    @PostMapping
    @ApiOperation("新增分账配置")
    public R save(@RequestBody SplitConfig splitConfig) {
        splitConfigService.save(splitConfig);
        return R.ok();
    }

    @PutMapping
    @ApiOperation("更新分账配置")
    public R update(@RequestBody SplitConfig splitConfig) {
        splitConfigService.updateById(splitConfig);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除分账配置")
    public R delete(@PathVariable Long id) {
        splitConfigService.removeById(id);
        return R.ok();
    }
} 