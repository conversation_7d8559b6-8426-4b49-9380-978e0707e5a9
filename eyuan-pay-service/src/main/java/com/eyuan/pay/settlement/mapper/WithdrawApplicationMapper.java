package com.eyuan.pay.settlement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eyuan.pay.settlement.entity.WithdrawApplication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 提现申请
 *
 * <AUTHOR>
 * @date 2024-05-15 10:30:00
 */
@Mapper
public interface WithdrawApplicationMapper extends BaseMapper<WithdrawApplication> {
    
    /**
     * 分页查询提现申请
     * @param page 分页对象
     * @param accountId 账户ID
     * @param accountType 账户类型
     * @return 分页结果
     */
    IPage<WithdrawApplication> pageWithdraws(IPage<WithdrawApplication> page, 
                                            @Param("accountId") String accountId,
                                            @Param("accountType") Integer accountType);
}