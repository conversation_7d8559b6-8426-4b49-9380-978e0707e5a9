package com.eyuan.pay.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dog.common.core.util.AssertUtil;
import com.eyuan.pay.settlement.entity.SettlementDetail;
import com.eyuan.pay.settlement.mapper.SettlementDetailMapper;
import com.eyuan.pay.settlement.service.SettlementDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 结算明细服务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class SettlementDetailServiceImpl extends ServiceImpl<SettlementDetailMapper, SettlementDetail> implements SettlementDetailService {

    @Override
    public SettlementDetail getByOrderNo(String orderNo) {
        AssertUtil.notBlank(orderNo, "参数错误，订单号不能为空");
        return this.baseMapper.selectOne(new LambdaQueryWrapper<SettlementDetail>().eq(SettlementDetail::getOrderNo, orderNo));
    }
} 