package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 分账操作日志
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@Data
@TableName("split_log")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "分账操作日志")
public class SplitLog extends Model<SplitLog> {
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId
    private Long id;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 分账明细ID
     */
    private Long splitDetailId;

    /**
     * 内容摘要
     */
    private String summary;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private String deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;
} 