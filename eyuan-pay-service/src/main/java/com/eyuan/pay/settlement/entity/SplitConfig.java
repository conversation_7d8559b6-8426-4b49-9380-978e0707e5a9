package com.eyuan.pay.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 供应商分账配置
 *
 * <AUTHOR>
 * @date 2023-10-01 12:00:00
 */
@Data
@TableName("split_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商分账配置")
public class SplitConfig extends Model<SplitConfig> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 分账模式（direct/settlement）
     */
    private String splitMode;

    /**
     * 分成类型（ratio/fixed）
     */
    private String splitType;

    /**
     * 平台分成比例(%)
     */
    private BigDecimal platformRatio;

    /**
     * 供应商分成比例(%)
     */
    private BigDecimal supplierRatio;

    /**
     * 平台固定分成金额
     */
    private BigDecimal platformFixed;

    /**
     * 供应商固定分成金额
     */
    private BigDecimal supplierFixed;

    /**
     * 订单核销后N天结算
     */
    private Integer settlementAfterDays;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private String deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;
} 