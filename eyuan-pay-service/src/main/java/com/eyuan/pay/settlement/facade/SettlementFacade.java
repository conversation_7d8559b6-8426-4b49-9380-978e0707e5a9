package com.eyuan.pay.settlement.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dog.common.core.context.TenantHolder;
import com.dog.common.core.context.UserHolder;
import com.dog.common.core.util.AssertUtil;
import com.dog.common.core.util.R;
import com.eyuan.pay.enums.*;
import com.eyuan.pay.response.SettlementCreateResponse;
import com.eyuan.pay.response.SettlementResponse;
import com.eyuan.pay.request.SettlementCreateRequest;
import com.eyuan.pay.settlement.dto.SettlementRequest;
import com.eyuan.pay.settlement.dto.SettlementAuditDTO;
import com.eyuan.pay.settlement.entity.SettlementConfig;
import com.eyuan.pay.settlement.entity.SettlementDetail;
import com.eyuan.pay.settlement.handler.SettlementHandleFactory;
import com.eyuan.pay.settlement.handler.SettlementHandler;
import com.eyuan.pay.settlement.mapper.SettlementDetailMapper;
import com.eyuan.pay.settlement.producer.SettlementResultNoticeSender;
import com.eyuan.pay.settlement.service.SettlementConfigService;
import com.eyuan.pay.settlement.service.SettlementDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.eyuan.pay.request.SettlementNoticeRequest;
import com.eyuan.pay.settlement.producer.SettlementAuditNoticeSender;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 结算业务聚合
 * @Author: sunwh
 * @Date: 2025/5/16 17:23
 */
@Service
@AllArgsConstructor
@Slf4j
public class SettlementFacade {
    private final SettlementConfigService settlementConfigService;
    private final SettlementDetailService settlementDetailService;
    private final SettlementHandleFactory settlementHandleFactory;
    private final SettlementAuditNoticeSender settlementAuditNoticeSender;
    private final SettlementDetailMapper settlementDetailMapper;
    private final SettlementResultNoticeSender settlementResultNoticeSender;

    /**
     * 创建结算信息
     * @param settlementCreateRequest
     * @return SettlementCreateResponse
     */
    @Transactional(rollbackFor = Exception.class)
    public SettlementCreateResponse createSettlement(SettlementCreateRequest settlementCreateRequest) {
        AssertUtil.notBlank(settlementCreateRequest.getSupplierId(), "供应商ID不能为空");
        AssertUtil.notBlank(settlementCreateRequest.getOrderNo(), "订单号不能为空");
        AssertUtil.notNull(settlementCreateRequest.getSettlementAmount(), "结算金额不能为空");
        AssertUtil.notNull(settlementCreateRequest.getTenantId(), "租户ID不能为空");
        AssertUtil.notBlank(settlementCreateRequest.getOrderType(), "订单类型不能为空");
        TenantHolder.setTenantId(settlementCreateRequest.getTenantId());

        SettlementConfig settlementConfig = settlementConfigService.getBySupplierId(settlementCreateRequest.getSupplierId());
        AssertUtil.notNull(settlementConfig, "供应商结算配置不存在");
        settlementCreateRequest.setSettleTo(settlementConfig.getSettleTo());
        Integer settlementAfterDays = settlementConfig.getSettlementAfterDays();
        DateTime dateTime = DateUtil.offsetDay(new Date(), settlementAfterDays);
        settlementCreateRequest.setSettlementTime(dateTime.toLocalDateTime());


        SettlementDetail settlementDetail = new SettlementDetail();
        settlementDetail.setOrderType(settlementCreateRequest.getOrderType());
        settlementDetail.setType(settlementCreateRequest.getSettleTo());
        settlementDetail.setSupplierId(settlementCreateRequest.getSupplierId());
        settlementDetail.setSupplierName(settlementCreateRequest.getSupplierName());
        settlementDetail.setAccountNo(settlementCreateRequest.getAccountNo());
        settlementDetail.setOrderNo(settlementCreateRequest.getOrderNo());
        settlementDetail.setOrderType(settlementCreateRequest.getOrderType());
        settlementDetail.setSettlementAmount(settlementCreateRequest.getSettlementAmount() );
        settlementDetail.setSettlementTime(ObjectUtil.isNotNull(settlementCreateRequest.getSettlementTime()) ?
                settlementCreateRequest.getSettlementTime() : LocalDateTime.now());
        settlementDetail.setStatus(SettlementStatusEnum.INIT.getValue());
        settlementDetailService.save(settlementDetail);

        SettlementCreateResponse settlementCreateResponse = BeanUtil.toBean(settlementDetail, SettlementCreateResponse.class);
        return settlementCreateResponse;
    }

    /**
     * 定时任务扫描待结算数据
     */
    public void scanPendingSettlements() {
        log.info("开始扫描待结算数据");

        LocalDateTime now = LocalDateTime.now();
        // 使用Mapper方法查询待结算数据，去除租户过滤
        List<SettlementDetail> pendingSettlements = settlementDetailMapper.findPendingSettlements(
                SettlementStatusEnum.INIT.getValue(), now);

        // 处理待结算数据
        if (CollUtil.isNotEmpty(pendingSettlements)) {
            pendingSettlements.forEach(detail -> {
                // 判断是否需要立即结算
//                TenantHolder.setTenantId(detail.getTenantId());
//                doSettlement(detail);

                // 更新状态为待结算
                detail.setStatus(SettlementStatusEnum.PENDING.getValue());
                settlementDetailService.updateById(detail);
            });
        }

        log.info("扫描待结算数据结束");
    }

    /**
     * 结算审核业务
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditSettlement(SettlementAuditDTO dto) {
        SettlementDetail detail = settlementDetailService.getById(dto.getId());
        if (detail == null) {
            throw new IllegalArgumentException("结算明细不存在");
        }
        // 1. 判断当前审核状态
        if (!SettlementAuditStatusEnum.PENDING.getValue().equals(detail.getAuditStatus())) {
            throw new IllegalStateException("当前状态不可审核");
        }
        // 2. 更新审核信息
        detail.setAuditStatus(dto.getAuditStatus());
        log.info("审核结算明细, 审核人信息: {}", UserHolder.getUser());
//        String user = StrUtil.isNotBlank(dto.getUser()) ? dto.getUser() : UserHolder.getUser().getAccName();
//        detail.setAuditUser(user);
        detail.setAuditRemark(dto.getRemark());
        detail.setAuditTime(LocalDateTime.now());
        detail.setStatus(SettlementStatusEnum.PROCESSING.getValue());
        settlementDetailService.updateById(detail);
        
        // 3. 审核通过则执行结算
        if (SettlementAuditStatusEnum.APPROVED.getValue().equals(dto.getAuditStatus())) {
            SettlementResponse settlementResponse = this.processSettlement(detail);
            SettlementDetail updateSettlementDetail = new SettlementDetail();
            updateSettlementDetail.setId(detail.getId());
            updateSettlementDetail.setTradeNo(settlementResponse.getTradeNo());
            updateSettlementDetail.setThirdTradeNo(settlementResponse.getThirdTradeNo());
            settlementDetailService.updateById(updateSettlementDetail);
        } else {
            // 4. 拒绝，发送审核结果通知
            this.sendSettlementStatusNotice(detail);
        }
    }

    private SettlementResponse processSettlement(SettlementDetail detail) {
        SettlementRequest settlementRequest = BeanUtil.toBean(detail, SettlementRequest.class);
        SettlementHandler settlementHandler = settlementHandleFactory.getSettlementHandler(detail.getType());
        R result = settlementHandler.execute(settlementRequest);
        AssertUtil.isTrue(result.success(),"结算失败:"+result.getMsg());
        return (SettlementResponse)result.getData();
    }

    /**
     * 更新结算状态并通知业务系统
     * @param tradeNo 交易号
     * @param status 结算状态
     * @param failReason 失败原因（如果有）
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSettlementStatusAndNotify(String tradeNo, Boolean status, String failReason) {
        log.info("更新结算状态并通知业务系统, tradeNo: {}, status: {}", tradeNo, status);
        // 1. 获取结算明细
        SettlementDetail settlementDetail = settlementDetailService.getByOrderNo(tradeNo);

        // 2. 更新结算状态
        String settlementStatus = status ? SettlementStatusEnum.SUCCESS.getValue() : SettlementStatusEnum.FAILED.getValue();
        SettlementDetail updateDetail = new SettlementDetail();
        settlementDetail.setId(settlementDetail.getId());
        updateDetail.setStatus(settlementStatus);
        if (!status && failReason != null) {
            updateDetail.setFailReason(failReason);
        }
        boolean updated = settlementDetailService.updateById(updateDetail);
        
        // 3. 发送结算状态通知
        sendSettlementStatusNotice(settlementDetail,settlementStatus,failReason);
    }

    private void sendSettlementStatusNotice(SettlementDetail detail,String settlementStatus,String failReason) {
        detail.setStatus(settlementStatus);
        detail.setFailReason(failReason);
        this.sendSettlementStatusNotice(detail);
    }

    /**
     * 发送结算状态通知
     * @param detail 结算明细
     */
    private void sendSettlementStatusNotice(SettlementDetail detail) {
        try {
            AppPlatformType appPlatformType = AppPlatformType.getByValue(detail.getOrderType());
            // 构建通知请求
            SettlementNoticeRequest noticeRequest = SettlementNoticeRequest.builder()
                    .orderNo(detail.getOrderNo())
                    .appPlatformType(appPlatformType)
                    .thirdOrderNo(detail.getThirdTradeNo())
                    .settlementAmount(detail.getSettlementAmount())
                    .settlementType(detail.getType())
                    .auditStatus(detail.getAuditStatus())
                    .tradeStatus(detail.getStatus())
                    .auditTime(detail.getAuditTime())
                    .auditRemark(detail.getAuditRemark())
                    .failReason(detail.getFailReason())
                    .tenantId(detail.getTenantId())
                    .tradeType(SettlementTradeTypeEnum.SETTLEMENT.getValue())
                    .userType(detail.getSupplierId())
                    .userId(AccountUserTypeEnum.SUPPLIER.getCode())
                    .build();
            
            // 发送通知到结算用户
            settlementAuditNoticeSender.send(noticeRequest);
            // 发送通知到业务模块
            settlementResultNoticeSender.send(noticeRequest);
            log.info("结算状态通知发送成功, settlementId: {}, status: {}", detail.getId(), detail.getStatus());
        } catch (Exception e) {
            log.error("发送结算状态通知异常, settlementId: {}", detail.getId(), e);
            // 这里可以选择抛出异常或者只记录日志
            // 如果是关键通知，建议抛出异常以便事务回滚
            // 如果是非关键通知，可以只记录日志
        }
    }
}
