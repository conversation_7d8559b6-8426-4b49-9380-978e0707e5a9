package com.eyuan.pay.settlement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dog.common.core.util.R;
import com.dog.common.log.annotation.SysLog;
import com.eyuan.pay.settlement.dto.WithdrawAuditDTO;
import com.eyuan.pay.settlement.entity.WithdrawApplication;
import com.eyuan.pay.settlement.facade.WithdrawFacade;
import com.eyuan.pay.settlement.service.WithdrawApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * 提现申请
 *
 * <AUTHOR>
 * @date 2024-05-15 10:30:00
 */
@RestController
@AllArgsConstructor
@RequestMapping("/withdrawapplication")
@Api(value = "withdrawapplication", tags = "提现申请管理")
public class WithdrawApplicationController {

    private final WithdrawApplicationService withdrawApplicationService;
    private final WithdrawFacade withdrawFacade;

    /**
     * 分页查询
     * @param page 分页对象
     * @param withdrawApplication 提现申请
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getWithdrawApplicationPage(Page page, WithdrawApplication withdrawApplication) {
        return R.ok(withdrawApplicationService.page(page, Wrappers.query(withdrawApplication)));
    }

    /**
     * 分页查询提现申请
     * @param page 分页对象
     * @param accountId 账户ID
     * @param accountType 账户类型
     * @return
     */
    @ApiOperation(value = "分页查询提现申请", notes = "分页查询提现申请")
    @GetMapping("/pageWithdraws")
    public R pageWithdraws(Page<WithdrawApplication> page,
                           @RequestParam(value = "accountId", required = false) String accountId,
                           @RequestParam(value = "accountType", required = false) Integer accountType) {
        return R.ok(withdrawApplicationService.pageWithdraws(page, accountId, accountType));
    }

    /**
     * 通过id查询提现申请
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        return R.ok(withdrawApplicationService.getById(id));
    }

    /**
     * 审核提现申请
     * @param withdrawAuditDTO 提现申请DTO
     * @return R
     */
    @ApiOperation(value = "审核提现申请", notes = "审核提现申请")
    @SysLog("审核提现申请")
    @PostMapping("/audit")
    public R auditWithdraw(@RequestBody WithdrawAuditDTO withdrawAuditDTO) {
        withdrawFacade.auditWithdraw(withdrawAuditDTO);
        return R.ok();
    }


    /**
     * 通过提现单号查询
     * @param withdrawNo 提现单号
     * @return R
     */
    @ApiOperation(value = "通过提现单号查询", notes = "通过提现单号查询")
    @GetMapping("/getByWithdrawNo/{withdrawNo}")
    public R getByWithdrawNo(@PathVariable("withdrawNo") String withdrawNo) {
        return R.ok(withdrawApplicationService.getOne(Wrappers.<WithdrawApplication>lambdaQuery()
                .eq(WithdrawApplication::getWithdrawNo, withdrawNo)));
    }

    /**
     * 查询待审核的提现申请
     * @param page 分页对象
     * @return R
     */
    @ApiOperation(value = "查询待审核的提现申请", notes = "查询待审核的提现申请")
    @GetMapping("/pendingAudit")
    public R getPendingAuditWithdraws(Page<WithdrawApplication> page) {
        return R.ok(withdrawApplicationService.page(page, Wrappers.<WithdrawApplication>lambdaQuery()
                .eq(WithdrawApplication::getStatus, 0)
                .orderByAsc(WithdrawApplication::getCreateTime)));
    }

    /**
     * 查询待打款的提现申请
     * @param page 分页对象
     * @return R
     */
    @ApiOperation(value = "查询待打款的提现申请", notes = "查询待打款的提现申请")
    @GetMapping("/pendingPayment")
    public R getPendingPaymentWithdraws(Page<WithdrawApplication> page) {
        return R.ok(withdrawApplicationService.page(page, Wrappers.<WithdrawApplication>lambdaQuery()
                .eq(WithdrawApplication::getStatus, 3)
                .orderByAsc(WithdrawApplication::getAuditTime)));
    }
}
