package com.eyuan.pay.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 查询支付配置参数
 * @Author: sunwh
 * @Date: 2025/5/14 16:02
 */
@Data
@Builder
public class PaySceneSearchDTO {

    /**
     * 渠道标识，如WECHAT_PAY、ALIPAY、HELIPAY
     */
    @ApiModelProperty(value = "渠道标识，如WECHAT_PAY、ALIPAY、HELIPAY")
    private String channelId;

    /**
     * 支付类型，如：WXPAY、ALIPAY、UNIONPAY、QQPAY
     */
    @ApiModelProperty(value = "支付类型，如：WXPAY、ALIPAY、UNIONPAY、QQPAY")
    private String payType;

    /**
     * 支付场景，如：APPLET、WAP、APP、SCAN、PUBLIC
     */
    @ApiModelProperty(value = "支付场景，如：APPLET、WAP、APP、SCAN、PUBLIC")
    private String payScene;
}
