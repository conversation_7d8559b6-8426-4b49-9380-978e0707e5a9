package com.eyuan.pay.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sunwh
 * @Date: 2025/5/14 17:11
 */
@Data
public class PaySceneDTO {

    /**
     * 渠道标识，如WECHAT_PAY、ALIPAY、HELIPAY
     */
    @ApiModelProperty(value = "渠道标识，如WECHAT_PAY、ALIPAY、HELIPAY")
    private String channelId;

    /**
     * 渠道名称，如微信支付、支付宝、合利宝
     */
    @ApiModelProperty(value = "渠道名称，如微信支付、支付宝、合利宝")
    private String channelName;

    /**
     * 商户号
     */
    @ApiModelProperty(value = "商户号")
    private String merchantId;

    /**
     * 商户密钥
     */
    @ApiModelProperty(value = "商户密钥")
    private String merchantKey;

    /**
     * 子商户号，用于微信服务商模式
     */
    @ApiModelProperty(value = "子商户号，用于微信服务商模式")
    private String subMerchantId;


    /**
     * 子应用ID，用于微信服务商模式
     */
    @ApiModelProperty(value = "子应用ID，用于微信服务商模式")
    private String subAppId;

    /**
     * 私钥
     */
    @ApiModelProperty(value = "私钥")
    private String privateKey;

    /**
     * 公钥
     */
    @ApiModelProperty(value = "公钥")
    private String publicKey;

    /**
     * 支付场景，如：APPLET、WAP、APP、SCAN、PUBLIC
     */
    @ApiModelProperty(value = "支付场景，如：APPLET、WAP、APP、SCAN、PUBLIC")
    private String payScene;


    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    private String payType;

    /**
     * appId
     */
    @ApiModelProperty(value = "appId")
    private String appId;

    /**
     * 支付模式，normal-普通商户模式，service-服务商模式
     */
    @ApiModelProperty(value = "支付模式，normal-普通商户模式，service-服务商模式")
    private String mode;

    /**
     * 状态，T-启用，F-禁用
     */
    @ApiModelProperty(value = "状态，T-启用，F-禁用")
    private String state;


}
