package com.eyuan.pay.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付通知参数
 * @Author: sunwh
 * @Date: 2025/5/26 16:21
 */
@Data
@Builder
public class TradeNoticeDTO {
    private String tradeNo;
    private String outTradeNo;
    private Boolean tradeStatus;
    private String failReason;
    private BigDecimal amount;
    private Date tradeTime;
    private Integer payChannel;
    private String platformType;
    private String tenantId;
    private Integer settlementTradeType;
}
