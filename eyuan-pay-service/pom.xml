<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.eyuan</groupId>
        <artifactId>eyuan-pay</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>eyuan-pay-service</artifactId>
    <description>支付业务</description>

    <dependencies>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-sequence</artifactId>
        </dependency>
        <!--spring security-->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-security</artifactId>
        </dependency>
        <!--feign 接口-->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-upms-api</artifactId>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-swagger</artifactId>
        </dependency>
        <!-- log -->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-log</artifactId>
        </dependency>
        <!-- 支付依赖-->
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-All</artifactId>
            <version>********</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.35.79.ALL</version> <!-- 使用最新版本 -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>

        <!--工具类核心包-->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-core</artifactId>
        </dependency>


        <!--springmvc-->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-redis</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.eyuan.home</groupId>-->
<!--            <artifactId>eyuan-mall-home-api</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->

        <!-- mybatis-plus  -->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-mybatisplus</artifactId>
        </dependency>

        <!-- springmvc -->
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>dog-common-springmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eyuan</groupId>
            <artifactId>eyuan-pay-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- redis -->
<!--        <dependency>-->
<!--            <groupId>com.cloud</groupId>-->
<!--            <artifactId>dog-common-redis</artifactId>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <!-- 过滤后缀为pem、pfx的证书文件 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
                <version>3.1.0</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>