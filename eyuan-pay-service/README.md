# 电商平台分账系统设计方案

## 一、设计原则

- **合规性**：符合中国人民银行支付业务相关规定
- **安全性**：资金操作有多重验证机制
- **可追溯**：所有分账操作完整记录日志
- **灵活性**：支持不同供应商采用不同分账方式

## 二、核心表结构

- 供应商分账配置表（split_config）
- 分账明细表（split_detail）
- 结算批次表（settlement_batch）
- 结算明细表（settlement_detail）
- 分账操作日志表（split_log）

## 三、业务流程

### 1. 支付分账流程

1. 用户下单并支付，平台收到全额款项
2. 系统根据分账配置，计算平台与供应商分成
3. 生成支付分账明细（split_detail，分账类型=pay）
4. 到结算周期，批量发起结算请求（settlement_batch/settlement_detail）
5. 更新结算明细和批次状态，记录分账日志
6. 分账或结算失败的，记录失败原因，进入异常处理流程

### 2. 退款分账流程

1. 用户发起退款，平台原路退回用户款项
2. 系统根据原分账明细，计算需回退的分账金额
3. 生成退款分账明细（split_detail，分账类型=refund，关联原分账明细ID）
4. 若已结算，则需从供应商账户回收资金或做负向结算
5. 更新分账明细和结算明细状态，记录分账日志
6. 退款分账或结算失败的，记录失败原因，进入异常处理流程

### 3. 结算流程

1. 结算周期到达，系统汇总所有已完成分账的明细
2. 生成结算批次和结算明细
3. 通过合利宝API发起结算
4. 更新结算明细和批次状态，记录分账日志
5. 结算失败的，记录失败原因，进入异常处理流程

## 四、分账配置与管理

- 支持分账模式、比例、结算周期等灵活配置
- 结算管理页面统一管理所有结算批次和明细
- 分账明细页面支持按“分账类型”筛选（支付/退款）
- 所有操作均有日志记录，便于追溯

## 五、限制与风控

- 不支持实时分账，按结算周期批量处理
- 单笔分账不低于1元，平台分成比例不低于5%
- 单日结算总额不超过50万元
- 不同供应商数据严格隔离

---